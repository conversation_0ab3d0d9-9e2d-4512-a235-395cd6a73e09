<!DOCTYPE html>
<html>
<head>
    <title>Test Migration Redirect</title>
</head>
<body>
    <h1>Test Migration Redirect</h1>
    <p>This is a test page to verify the migration redirect functionality.</p>
    
    <script>
        // Mock klook object for testing
        window.klook = {
            pmsGet: async function(url) {
                console.log('Mock API call to:', url);
                
                // Simulate API response with has_migrated = true
                return {
                    result: {
                        protect_status: 0,
                        has_migrated: true,
                        editor_id: 9999,
                        editor_name: "System"
                    }
                };
            },
            parseAjaxUrl: function(url) {
                return url;
            }
        };
        
        // Mock URL constructor for older browsers
        if (!window.URL) {
            window.URL = function(url) {
                var a = document.createElement('a');
                a.href = url;
                this.searchParams = {
                    entries: function() {
                        var params = [];
                        if (a.search) {
                            var pairs = a.search.substring(1).split('&');
                            for (var i = 0; i < pairs.length; i++) {
                                var pair = pairs[i].split('=');
                                params.push([decodeURIComponent(pair[0]), decodeURIComponent(pair[1] || '')]);
                            }
                        }
                        return params;
                    }
                };
            };
        }
        
        // Mock URLSearchParams for older browsers
        if (!window.URLSearchParams) {
            window.URLSearchParams = function() {
                this.params = {};
                this.set = function(key, value) {
                    this.params[key] = value;
                };
                this.toString = function() {
                    var pairs = [];
                    for (var key in this.params) {
                        pairs.push(encodeURIComponent(key) + '=' + encodeURIComponent(this.params[key]));
                    }
                    return pairs.join('&');
                };
            };
        }
        
        // Test function
        async function testMigrationRedirect() {
            console.log('Testing migration redirect...');
            
            // Set current URL to simulate the old admin page
            window.history.replaceState({}, '', '/act/content/basic/71171?edit_status=1&is_am=false&ref=zh_CN&lang=en_US');
            
            try {
                // Import and test the checkLock function
                // Note: In real environment, this would be imported from the actual module
                const activity_id = 71171;
                const params = { user_type: 1, language: 'en_US' };
                
                // Simulate the checkLock call
                const query = params ? '?' + Object.keys(params).map(k => k + '=' + params[k]).join('&') : '';
                const resp = await klook.pmsGet(`/prosrv/activities/${activity_id}/protectstatus${query}`);
                
                if (resp.result.has_migrated) {
                    const currentUrl = new URL(window.location.href);
                    const searchParams = currentUrl.searchParams;
                    
                    let redirectUrl = `/aid/attraction/${activity_id}/name-and-location`;
                    const urlParams = new URLSearchParams();
                    
                    for (const [key, value] of searchParams.entries()) {
                        urlParams.set(key, value);
                    }
                    
                    if (urlParams.toString()) {
                        redirectUrl += `?${urlParams.toString()}`;
                    }
                    
                    console.log('Would redirect to:', redirectUrl);
                    console.log('✅ Migration redirect test passed!');
                    
                    // Show result on page
                    document.body.innerHTML += `
                        <div style="background: #d4edda; border: 1px solid #c3e6cb; padding: 10px; margin: 10px 0; border-radius: 4px;">
                            <h3>✅ Test Passed!</h3>
                            <p><strong>Original URL:</strong> ${window.location.href}</p>
                            <p><strong>Redirect URL:</strong> ${redirectUrl}</p>
                        </div>
                    `;
                } else {
                    console.log('❌ Migration redirect test failed - has_migrated is false');
                }
            } catch (error) {
                console.error('Test error:', error);
                document.body.innerHTML += `
                    <div style="background: #f8d7da; border: 1px solid #f5c6cb; padding: 10px; margin: 10px 0; border-radius: 4px;">
                        <h3>❌ Test Failed!</h3>
                        <p><strong>Error:</strong> ${error.message}</p>
                    </div>
                `;
            }
        }
        
        // Run test when page loads
        window.onload = testMigrationRedirect;
    </script>
</body>
</html>

checkLock 中请求/prosrv/activities/${activity_id}/protectstatus${query}的 会新增返回字段，has_migrated=true 时，前端重定向到新版 Revamp Admin 景点编辑页

```
{
    "protect_status": 1, // 0-未被保护 1-被保护且不可阅读 2-被保护但可以阅读
    "editor_id": 9999,
    "editor_name": "System",
    // 当not_allowed_edit_hint不为空时，优先展示not_allowed_edit_hint的提示文案
    "not_allowed_edit_hint": "Sorry, the activity is being migrated, so you cannot edit it."
    // has_migration=true时，前端重定向到新版Revamp Admin景点编辑页
     "has_migrated": true
}
```

现在的url为/act/content/basic/71171?edit_status=1&is_am=false&ref=zh_CN&lang=en_US

重定向的url为 /aid/attraction/75401/name-and-location 

请求参数保留

/*
 ## expected behaviour
 - when user is on the page, check editlock, sent edit protect ajax
 - when user is not on the page, no ajax sent,
 */

/*
 * active status include `focus` and `active`, focus will also trigger active, so we only listen to active
 * inactive status: blur, idle, hidden. are diffenert event, but we don't care
 */
var ifvisible = require("ifvisible");

const protect_interval = 120; // seconds
const idle_duration = 180; // seconds

function setParams(params = {}) {
  if (toString.call(params) !== "[object Object]") {
    alert("Error params");
    window.history.back(-1);
    return;
  }

  let query = new URLSearchParams();
  Object.entries(params).forEach(item => {
    let [key, val] = item;
    query.set(key, val);
  });

  return query.toString();
}

let interval;

async function checkLock(activity_id, page_back, callback, params = null) {
  let query = params ? `?${setParams(params)}` : "";

  // // 测试环境关闭编辑锁
  // if (!klook.isPrd) {
  //   return;
  // }

  var resp = await klook.pmsGet(
    klook.parseAjaxUrl(
      `/prosrv/activities/${activity_id}/protectstatus${query}`
    )
  );

  if (resp.result.protect_status == 0) {
    if (callback instanceof Function) {
      callback(resp);
    }
    return resp;
  } else if (resp.result.protect_status == 1) {
    const str = resp.result?.not_allowed_edit_hint;
    klook.popupDialog(str || `${resp.result.editor_name} ${__("act.is.editing")}`);
    page_back && window.setTimeout(back, 3000);
    klook.abort();
  }
}

async function init(
  activity_id,
  params = null,
  { page_back = true, callback = null } = {}
) {
  //测试环境关闭编辑锁
  if (!klook.isPrd) {
    return;
  }

  ifvisible.setIdleDuration(idle_duration);

  // page load won't trigger event, manually check status immediatly
  if (ifvisible.now()) {
    // await checkLock(activity_id, true, null, params);
    await checkLock(activity_id, page_back, callback, params);
  }

  let query = params ? `?${setParams(params)}` : "";

  interval = ifvisible.onEvery(protect_interval, async () => {
    await klook.pmsGet(
      klook.parseAjaxUrl(
        `/prosrv/activities/${activity_id}/editprotect${query}`
      )
    );
  });
  ifvisible.on("wakeup", async function () {
    // checkLock(activity_id, true, null, params);
    await checkLock(activity_id, page_back, callback, params);
  });
}

function back() {
  if (window.history.length > 1) {
    window.history.back();
  } else {
    window.close();
  }
}

// what use ??
function unlock(activity_id, params = null) {
  let query = params ? `?${setParams(params)}` : "";
  klook.ajaxGet(
    klook.parseAjaxUrl("/prosrv/activities/" + activity_id + `/unlock${query}`)
  );

  ifvisible.off("wakeup");
  interval && interval.stop();
}

module.exports = {
  checkLock,
  init,
  unlock
};

<template>
  <div
    class="manage-lang manage-language-wrapper"
    :class="{ 'is-edit': !is_edit }"
  >
    <!-- 编辑语言 -->
    <el-dialog
      :title="
        is_edit
          ? $t('manage_language_manage_language')
          : $t('manage_language_status')
      "
      :visible.sync="dialogTableVisible"
      @close="refresh"
    >
      <p class="css-note header__note" style="font-size: smaller">
        <span v-html="$t('multi_en_hint')" />
      </p>

      <div class="header__button">
        <template v-for="[key, val] in Object.entries(BATCH_CONF)">
          <el-button
            :key="key"
            type="text"
            size="small"
            :disabled="disabledBatch(key)"
            @click="handleBatch(key)"
          >
            {{ val }}
          </el-button>
        </template>
      </div>
      <el-table :data="tableData" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55"> </el-table-column>
        <!-- 语言 -->
        <el-table-column key="language" :label="$t('global_language')">
          <template scope="scope">
            {{ fmt(scope.row.language) }}
          </template>
        </el-table-column>
        <!-- 语言翻译状态 !is_edit-->
        <el-table-column
          v-if="!is_edit"
          key="translation_status"
          :label="$t('act_list_translation_status')"
        >
          <template scope="scope">
            <!-- 修改翻译状态 -->
            <ModifyTranslation
              v-model="scope.row.translation_status"
              :activity-id="id"
              :config="scope.row"
              :visible="dialogTableVisible"
            />
          </template>
        </el-table-column>
        <!-- 语言发布状态 !is_edit-->
        <el-table-column
          key="publish_status"
          :label="$t('act_list_language_publish_status')"
        >
          <template scope="scope">
            <div class="text-wrap">
              <span>{{ getPublishStatusLabel(scope.row) }}</span>
              <!-- 按英语发布, 语言状态为草稿, 按部分英语发布, 语言状态为5, -->
              <span
                v-if="scope.row.status === 1 || scope.row.status === 5"
                style="color: gray"
              >
                {{ $t("manage_language_modify_detail") }}
                {{ scope.row.author }}
                {{ scope.row.update_time }}
              </span>
              <el-button
                v-if="scope.row.status"
                type="text"
                size="mini"
                @click="reviewEventLogs(scope.row)"
              >
                {{ $t("activity_event_logs") }}
              </el-button>
            </div>
          </template>
        </el-table-column>
        <!-- 活动修改 -->
        <el-table-column key="modify" :label="$t('package_list_modify')">
          <template scope="scope">
            <template v-if="is_edit">
              <template v-if="!edit_lang_selected">
                <!-- 新增 -->
                <el-tooltip
                  :disabled="!is_in_block_list(scope.row)"
                  placement="right"
                  :content="$t('act_list_is_on_blocklist')"
                >
                  <el-button
                    v-if="scope.row.status == 0"
                    size="small"
                    type="text"
                    :disabled="noEditLang(scope.row)"
                    @click="add(scope.$index, scope.row)"
                  >
                    {{ $t("global_button_add") }}
                  </el-button>
                  <!-- 编辑 -->
                  <el-button
                    v-else
                    size="small"
                    type="text"
                    :disabled="noEditLang(scope.row)"
                    @click="edit(scope.$index, scope.row)"
                  >
                    {{ $t("global_button_edit") }}
                  </el-button>
                </el-tooltip>
                <el-tooltip
                  v-if="noEditLang(scope.row)"
                  class="item"
                  effect="dark"
                  :content="$t('no_edit_en')"
                  placement="top-start"
                >
                  <i class="el-icon-warning" />
                </el-tooltip>
              </template>
              <el-button
                v-if="edit_lang == scope.row.language"
                size="small"
                type="text"
                @click="edit_lang = ''"
              >
                {{ $t("global_button_cancel") }}
              </el-button>
            </template>
            <template v-else>
              <!-- TEMP hide this copy function for no op power -->
              <!-- <el-button v-if="!is_wifi && copyReady(scope.row)" size="small" type="text" @click="copyEnglish(scope.row)">{{$t('copy_multi_english')}}</el-button> -->
              <!--draft => Submit to Preview-->
              <!-- 发布按钮 -->
              <el-tooltip
                v-if="scope.row.status == 2"
                :disabled="!is_in_block_list(scope.row)"
                placement="right"
                :content="$t('act_list_is_on_blocklist')"
              >
                <span>
                  <el-button
                    :disabled="
                      is_in_block_list(scope.row) || isTranslatingRow(scope.row)
                    "
                    size="small"
                    type="text"
                    @click="
                      change_publish(
                        scope.$index,
                        scope.row,
                        true,
                        false,
                        false
                      )
                    "
                  >
                    {{ $t("global_publish") }}
                  </el-button>
                </span>
              </el-tooltip>
              <!-- 取消发布按钮 -->
              <el-button
                v-else-if="scope.row.status == 3"
                size="small"
                type="text"
                @click="
                  change_publish(scope.$index, scope.row, false, false, false)
                "
              >
                {{ $t("global_unpublish_1") }}
              </el-button>
              <!-- 发布按钮 -->
              <el-tooltip
                v-else-if="scope.row.status == 4"
                :disabled="
                  !is_in_block_list(scope.row) || isTranslatingRow(scope.row)
                "
                placement="right"
                :content="$t('act_list_is_on_blocklist')"
              >
                <span>
                  <el-button
                    size="small"
                    type="text"
                    :disabled="
                      is_in_block_list(scope.row) || isTranslatingRow(scope.row)
                    "
                    @click="
                      change_publish(
                        scope.$index,
                        scope.row,
                        true,
                        false,
                        false
                      )
                    "
                  >
                    {{ $t("global_publish") }}
                  </el-button>
                </span>
              </el-tooltip>
              <!-- 预览按钮 -->
              <el-button
                v-else-if="
                  act_status !== 2 &&
                    scope.row.status === 1 &&
                    !am_page &&
                    !scope.row.default_show_english
                "
                size="small"
                type="text"
                @click="jumpToSubmit(scope)"
              >
                {{ $t("act_submit_to_preview") }}
              </el-button>
              <span v-else> - </span>
            </template>
          </template>
        </el-table-column>
        <!-- 英文内容 -->
        <el-table-column
          v-if="!is_edit"
          key="pub_english_content"
          :label="$t('act_english_content')"
          width="180"
        >
          <template scope="scope">
            <!-- 取消发布英文按钮 -->
            <el-button
              v-if="scope.row.default_show_english"
              size="small"
              type="text"
              @click="change_publish(scope.$index, scope.row, false, true)"
            >
              {{ $t("act_list_cancel_en_US_publish") }}
            </el-button>

            <template v-if="can_pub_en(scope.row)">
              <!-- 发布为美国英文按钮 -->
              <el-tooltip
                v-if="!scope.row.default_show_english"
                :disabled="
                  !is_in_block_list(scope.row) || isTranslatingRow(scope.row)
                "
                placement="right"
                :content="$t('act_list_is_on_blocklist')"
              >
                <span>
                  <el-button
                    size="small"
                    type="text"
                    :disabled="
                      is_in_block_list(scope.row) ||
                        scope.row.status === 5 ||
                        isTranslatingRow(scope.row)
                    "
                    @click="change_publish(scope.$index, scope.row, true, true)"
                  >
                    {{ $t("act_list_publish_with_en_US") }}
                  </el-button>
                </span>
              </el-tooltip>
            </template>
            <template v-else>
              -
            </template>
          </template>
        </el-table-column>
        <!-- 管理语言状态 => publish with ai -->
        <el-table-column
          v-if="!is_edit && show_publish_with_ai_column"
          key="publish_with_ai"
          :label="$t('act_list_publish_with_AI_translation')"
          :render-header="render_publish_with_ai_header"
        >
          <template scope="scope">
            <span v-if="sourceLanguage === scope.row.language">-</span>
            <el-button
              v-else-if="scope.row.status === 5"
              class="text-wrap button"
              size="small"
              type="text"
              :disabled="!canOperateUnpubAiBtn(scope.row)"
              @click="
                change_publish(scope.$index, scope.row, false, false, true)
              "
            >
              {{
                getButtonText(
                  $t("act_list_cancel_AI_translation_publish"),
                  scope.row.status
                )
              }}
            </el-button>
            <!-- 是否展示 publish with ai btn -->
            <!-- 已经按美国英文发布了, 不能点击按机翻发布 -->
            <el-tooltip
              v-else-if="show_pub_ai_btn(scope.row, sourceLanguage)"
              :disabled="!is_in_block_list(scope.row)"
              placement="right"
              :content="$t('act_list_is_on_blocklist')"
            >
              <span>
                <el-button
                  size="small"
                  type="text"
                  class="text-wrap button"
                  :disabled="
                    is_in_block_list(scope.row) ||
                      !can_operate_pub_ai_btn(scope.row)
                  "
                  @click="
                    change_publish(scope.$index, scope.row, true, false, true)
                  "
                >
                  {{
                    getButtonText(
                      $t("act_list_publish_with_AI_translation"),
                      scope.row.status
                    )
                  }}
                </el-button>
              </span>
            </el-tooltip>
            <span v-else>-</span>
          </template>
        </el-table-column>
      </el-table>
      <template slot="footer">
        <div v-if="edit_lang_selected" style="text-align: left;">
          {{ $t("manage_language_reference_lang") }}
          <el-radio-group v-model="ref_lang" class="ref">
            <el-radio
              v-for="item in ref_lang_options"
              :key="item.language"
              :label="item.language"
            >
              {{ fmt(item.language) }}
            </el-radio>
          </el-radio-group>
          <span v-if="ref_lang_options.length == 0">{{
            $t("ref_lang_none")
          }}</span>
        </div>
        <span v-if="act_status >= 0" style="float: left; margin-top: 10px;">{{
          $t("global_activity_status") + ": " + act_status_map[act_status]
        }}</span>
        <el-button style="float:right;margin:0 10px" @click="cancel">
          {{ $t("cancel") }}
        </el-button>
        <!-- FIXME known bug: confirm button may not show up on Safari -->
        <el-button
          v-if="edit_lang_selected"
          style="float:right"
          type="primary"
          @click="confirm"
        >
          {{ $t("confirm") }}
        </el-button>
      </template>
    </el-dialog>

    <el-dialog
      title="Copy English"
      :visible.sync="copyEnglishDialog"
      @close="$emit('confirm-copy', false)"
    >
      <el-form label-width="200px">
        <el-form-item>
          {{ $t("copy_english_hint") }}
        </el-form-item>
        <el-form-item label="Copy From:">
          {{ copy_from }}
        </el-form-item>
        <el-form-item label="Copy To:">
          <el-select v-model="copy_to" multiple filterable>
            <el-option
              v-for="lang in readyEnglishLangs"
              :key="lang"
              :label="lang"
              :value="lang"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="copyEnglishDialog = false">Cancel</el-button>
        <el-button type="primary" @click="confirmCopyEnglish"
          >Confirm</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import ModifyTranslation from "./modify_translation";
import { publishDraftMerchantActErrInterceptor } from "./activity/utils";

var statusTable = {
  // this is status for language, nothing like status for activity.
  0: __("package_language_none"),
  1: __("global_draft"),
  2: __("act_list_in_preview"),
  3: __("global_published"),
  4: __("global_unpublished"),
  5: __("79629")
};
// 获取语言翻译状态
async function getTranslateStatus(activity_id) {
  const res = await ADMIN_API.getActTranslateStatus(activity_id);
  const statuses = _.get(res, "result.statuses.0.items") || [];
  const status_obj = statuses.reduce((acc, cur) => {
    acc[cur.language] = cur.status;
    return acc;
  }, {});
  return status_obj;
}

var copy_english_mixin = {
  data() {
    return {
      copyEnglishDialog: false,
      copy_from: "",
      copy_to: ""
    };
  },
  computed: {
    readyEnglishLangs() {
      return this.tableData
        .filter(v => v.status < 2 && v.language.match("en"))
        .map(v => v.language);
    }
  },
  methods: {
    confirmCopyEnglish() {
      this.$emit("confirm-copy", true);
      this.copyEnglishDialog = false;
    }
  }
};

var visible_mixin = {
  data() {
    return {
      dialogTableVisible: true
    };
  },
  computed: {
    visible() {
      return this.$root.dialogVisible;
    }
  },
  watch: {
    visible() {
      this.dialogTableVisible = this.visible;
    },
    dialogTableVisible() {
      this.$root.dialogVisible = this.dialogTableVisible;
    }
  }
};

var init_data_mixin = {
  computed: {
    act_status() {
      return this.$root.act_status;
    },
    is_edit() {
      // this dialog is used both for manage language status or edit/add them. this is a flag.
      return this.$root.is_edit;
    },
    am_page() {
      return window.am_page;
    },
    is_am() {
      return this.$root.is_am;
    },
    has_em_access() {
      return window.KLK_PAGE_DATA._roles.has_em_access;
    },

    id() {
      return this.$root.activity_id;
    },
    rootData() {
      return this.$root.tableData;
    },
    template_id() {
      return this.$root.template_id;
    },
    // 美英是否结构化完成
    is_en_standardization_finished() {
      // 0-未结构化 1-结构化中 2-结构化完毕
      return this.$root.en_standardization_status === 2;
    },
    // 屏蔽的语言
    blocked_languages() {
      return this.$root.blocked_languages;
    },
    source_language() {
      return this.$root.sourceLanguage;
    },
    is_support_published_with_ai() {
      return this.$root.isSupportPublishedWithAi;
    }
  },
  watch: {
    rootData(next) {
      this.tableData = next;
    },
    source_language(v) {
      this.sourceLanguage = v;
    },
    is_support_published_with_ai(v) {
      this.isSupportPublishedWithAi = v;
    }
  }
};

const batch_mixin = {
  data() {
    return {
      multipleSelection: [],
      selectionResp: {
        publish: [],
        unpublish: [],
        publish_with_en_us: [],
        cancel_en_us_publish: []
      },
      BATCH_CONF: {
        publish: __("global_publish_1"),
        unpublish: __("global_unpublish_1"),
        publish_with_en_us: __("act_list_publish_with_en_US"),
        cancel_en_us_publish: __("act_list_cancel_en_US_publish")
      }
    };
  },
  watch: {
    multipleSelection: {
      deep: true,
      handler() {
        this.selectionResp = this.multipleSelection.reduce(
          (acc, curr) => {
            if ([2, 4].includes(curr.status) && !this.is_in_block_list(curr)) {
              acc = {
                ...acc,
                publish: [...acc.publish, curr]
              };
            }
            if ([3].includes(curr.status)) {
              acc = {
                ...acc,
                unpublish: [...acc.unpublish, curr]
              };
            }
            if (
              this.can_pub_en(curr) &&
              !(this.is_in_block_list(curr) || curr.status === 5)
            ) {
              acc = {
                ...acc,
                publish_with_en_us: [...acc.publish_with_en_us, curr]
              };
            }
            if (this.can_pub_en(curr) && curr.default_show_english) {
              acc = {
                ...acc,
                cancel_en_us_publish: [...acc.cancel_en_us_publish, curr]
              };
            }

            return acc;
          },
          {
            publish: [],
            unpublish: [],
            publish_with_en_us: [],
            cancel_en_us_publish: []
          }
        );
      }
    }
  },
  methods: {
    handleSelectionChange(val) {
      this.multipleSelection = val;
    },
    disabledBatch(type) {
      return !this.selectionResp[type].length;
    },
    async handleBatch(type) {
      const selectionResp = this.selectionResp[type];
      await this.$confirm(
        `${this.BATCH_CONF[type]}: ${selectionResp.map(item =>
          this.fmt(item.language)
        )}`
      );

      let do_publish = false,
        change_en_status = false;

      if (["publish", "publish_with_en_us"].includes(type)) {
        do_publish = true;
      }
      if (["publish_with_en_us", "cancel_en_us_publish"].includes(type)) {
        change_en_status = true;
      }

      if (change_en_status) {
        // 使用英语发布或取消发布 批量处理，避免死锁。
        await ADMIN_API.batchUpdateActLangStatus({
          data: {
            activity_id: +this.id,
            status: 1,
            language_list: selectionResp.map(item => item.language),
            default_show_en: type === "publish_with_en_us" ? "1" : "0" // "0"-取消发布美国英文, "1"-发布美国英文
          }
        });
        this.$message(__("global_success"));
      } else {
        const tableData = this.tableData;
        let needConfirm = false;
        if (!do_publish) {
          const published = tableData.filter(ele => ele.status == 3);
          // 取消发布 => 需要保留至少一个已发布语言
          if (published.length === selectionResp.length) {
            this.$message(__("act_list_cant_not_unpub"));
            return;
          }
          needConfirm = true;
        } else {
          needConfirm = selectionResp.some(item => !item.first_publish_time);
        }
        // 二次确认
        if (needConfirm) {
          const includeEn = selectionResp.find(
            item => item.language === "en_US"
          );
          const msg = this.getMessage(do_publish, includeEn?.language ?? "");
          const c = await this.confirmPromise(msg);
          if (c != "confirm") {
            return;
          }
        }

        let resp = await ADMIN_API.batchUpdateActLangStatus({
          data: {
            activity_id: +this.id,
            status: do_publish ? 3 : 4,
            language_list: selectionResp.map(item => item.language),
            default_show_en: ""
          }
        });

        if (resp?.success) {
          this.$message(__("global_success"));
        } else {
          this.$alert(`
            Partial succeed, ineligible languages: ${this.selectionResp[
              type
            ].map(item => this.fmt(item.language))} Please go ${
            this.BATCH_CONF[type]
          } the individual language to find out why.
          `);
        }

        // let resp = await Promise.all(
        //   this.selectionResp[type].map(row =>
        //     this.change_publish(null, row, do_publish, change_en_status, null, {
        //       just_throw: false,
        //       refresh: false
        //     })
        //   )
        // );

        // resp = resp.filter(item => item);
        // if (resp.length) {
        //   this.$alert(`
        //   Partial succeed, ineligible languages: ${this.selectionResp[type].map(
        //     item => this.fmt(item.language)
        //   )} Please go ${
        //     this.BATCH_CONF[type]
        //   } the individual language to find out why.
        // `);
        // } else {
        //   this.$message(__("global_success"));
        // }
      }

      let [
        {
          result: {
            status = [],
            source_language = "",
            is_support_published_with_ai = false
          }
        },
        translate_status_obj
      ] = await Promise.all([
        ADMIN_API.get_act_lang_status(this.id),
        getTranslateStatus(this.id)
      ]);
      status.forEach(v => {
        v.translation_status = translate_status_obj[v.language];
      });
      this.tableData = status;
      this.sourceLanguage = source_language;
      this.isSupportPublishedWithAi = is_support_published_with_ai;
    }
  }
};

export default {
  components: { ModifyTranslation },
  mixins: [copy_english_mixin, visible_mixin, init_data_mixin, batch_mixin],
  data() {
    return {
      sourceLanguage: "",
      tableData: [],
      edit_lang: "",
      ref_lang: "",
      add_flag: false,
      act_status_map: {
        0: __("global_unpublished"),
        1: __("global_published"),
        2: __("global_draft"),
        3: __("act_list_content_to_edit"),
        4: __("act_list_in_preview")
      }
    };
  },
  computed: {
    ref_lang_options() {
      return this.tableData.filter(
        ele =>
          ele.language != this.edit_lang &&
          ele.status > 0 &&
          (this.is_am || ele.status >= 2)
      );
    },
    edit_lang_selected() {
      return this.edit_lang != "";
    },
    is_wifi() {
      return this.$root.is_wifi;
    },
    hasViewEnUSPubAiPermission() {
      return klook.userHasPermission(
        "act/activity_list_page/am_table/language_status_publish_with_ai/view_en_us"
      );
    },
    hasViewOtherPubAiPermission() {
      return klook.userHasPermission(
        "act/activity_list_page/am_table/language_status_publish_with_ai/view"
      );
    },
    // 展示 publish with ai 列
    // 仅需要翻译title的Template. 表示除了活動標題，其他freetext無論是否存有翻譯皆以英文填充搭配機翻按鈕發布
    // 涉及的Template: Attraction&Show, Tours&Sightseeing, Activity&Experience, Transport&Travel Service
    // 列展示条件: 有查看权限, 且属于scope范围内的template
    show_publish_with_ai_column() {
      const hasPermission =
        this.hasViewEnUSPubAiPermission || this.hasViewOtherPubAiPermission;

      return (
        hasPermission && [1, 2, 3, 4, 5, 7, 8, 103].includes(this.template_id)
      );
    },
    hasOperateOtherPublishWithAiPermission() {
      return klook.userHasPermission(
        "act/activity_list_page/am_table/language_status_publish_with_ai/operate"
      );
    },
    hasOperateEnUSPublishWithAiPermission() {
      return klook.userHasPermission(
        "act/activity_list_page/am_table/language_status_publish_with_ai/operate_en_us"
      );
    }
  },
  watch: {
    edit_lang() {
      this.ref_lang = ""; // refresh
    }
  },
  methods: {
    isTranslatingRow(row) {
      return row.translation_status === -1;
    },
    // 是否是block的语言
    is_in_block_list(row) {
      return this.blocked_languages.includes(row.language);
    },
    can_pub_en(row) {
      // return !!this.tableData.find(ele => ele.language.match(/us/i) && ele.status === 3) && // has us && us already publshed
      return (
        !!this.tableData.find(
          ele => ele.language.match(/en_US/i) && [3, 5].includes(ele.status) // en_US published or publish with AI
        ) && // has us && us already publshed
        // !['US', 'TW', 'CN'].includes(row.language) && // current language not main language
        // !["en_US", "zh_TW", "zh_CN"].includes(row.language) && // current language not main language
        !["en_US", "zh_CN"].includes(row.language) && //! update: zh_TW can publish as en_US now
        row.status != 3
      ); // current language not published
    },
    // 满足条件展示 publish with ai 按钮
    show_pub_ai_btn(row, source) {
      // 仅需要翻译title的Template. 表示除了活動標題，其他freetext無論是否存有翻譯皆以英文填充搭配機翻按鈕發布
      // 涉及的语言: 除了11种EN和zh_CN以外的其他所有多语言
      // 展示条件: 活动en_US已发布 && EN-US已经完成结构化 && 当前语言翻译状态处于"Pending"和"Done"
      // 操作条件: 有操作权限
      const language = row.language;
      const isEnUS = language === "en_US";
      let flag = true;
      if (isEnUS) {
        flag = true;
      } else if (language.includes("en")) {
        flag = false;
      }

      let translationStatus;
      let hasPermission = true;
      if (isEnUS) {
        // 0: __("package_language_none"),
        // 1: __("global_draft"),
        // 2: __("act_list_in_preview"),
        // 3: __("global_published"),
        // 4: __("global_unpublished"),
        // 5: __("79629")
        // 如果en_US的发布状态为None/Draft/In preview，则en_US这一行可以展示Publish with AI按钮（不用判断翻译状态）
        translationStatus = [0, 1, 2].includes(row.status);
        hasPermission = this.hasViewEnUSPubAiPermission;
      } else {
        // translation_status: 0 none, 1: pending, 2: done
        // 翻译状态为 pending || done [pending: 部分翻译完成(包括title), done: 全部翻译完成]
        hasPermission = this.hasViewOtherPubAiPermission;
        translationStatus = [1, 2].includes(row.translation_status);
      }

      const arr = [
        flag,
        // !row.language.includes("en"), // 非英文系列
        // row.language !== "zh_CN", // 非简中
        // translation_status: 0 none, 1: pending, 2: done
        hasPermission,
        translationStatus,
        !!this.tableData.find(
          ele => ele.language === source && ele.status === 3
        ) // 有美国英文且已发布
        // this.is_en_standardization_finished // 英语已结构化完成
      ];
      return arr.every(v => v);
    },
    canOperateUnpubAiBtn(row) {
      return row.language === "en_US"
        ? this.hasOperateEnUSPublishWithAiPermission
        : this.hasOperateOtherPublishWithAiPermission;
    },
    // 控制 publish with ai 按钮置灰状态
    can_operate_pub_ai_btn(row) {
      // 3: __("global_published"),
      // 4: __("global_unpublished"),
      let statusFlag;
      const isEnUS = row.language === "en_US";
      let hasPermission = true;
      if (isEnUS) {
        // 在local language状态为published/unpublished的情况下，en_US的Publish with AI按钮可点击
        statusFlag = this.tableData.find(
          ele =>
            ele.language === this.sourceLanguage && [3, 4].includes(ele.status)
        );
        hasPermission = this.hasOperateEnUSPublishWithAiPermission;
      } else {
        statusFlag = row.status !== 3; // 未发布
        hasPermission = this.hasOperateOtherPublishWithAiPermission;
      }
      const arr = [
        hasPermission,
        row.default_show_english !== 1, // 未按美国英文发布
        statusFlag
      ];
      return arr.every(v => v);
    },
    fmt: lang => lang_conf.getLangObj("B_LANG", "LANG_TITLE")[lang] || lang,
    statusFmt: status => statusTable[status],
    confirmPromise(msg) {
      return this.$msgbox({
        title: " ",
        message: msg,
        showCancelButton: true,
        confirmButtonText: __("global_confirm"),
        cancelButtonText: __("global_cancel")
      });
    },
    getMessage(do_publish, lang) {
      if (do_publish) {
        return lang === "en_US" ? __("80729") : __("80730");
      }
      return lang === "en_US" ? __("80731") : __("80732");
    },
    getConfirmStatus(row, do_publish) {
      const { first_publish_time, language } = row;
      const scoure = this.sourceLanguage;
      const isSupportPublishedWithAi = this.isSupportPublishedWithAi;
      if (!do_publish) {
        return language === scoure;
      }
      return (
        isSupportPublishedWithAi && !first_publish_time && language === "en_US"
      );
    },
    async change_publish(
      index,
      row,
      do_publish,
      change_en_status,
      pubilsh_with_ai,
      { just_throw = true, refresh = true } = {}
    ) {
      // 取消发布 => 需要保留至少一个已发布语言
      if (!do_publish) {
        if (
          !this.tableData.find(
            ele => ele.status == 3 && ele.language != row.language
          )
        ) {
          // can not unpub the only published lang
          // this.$alert(__("act_list_cant_not_unpub"));
          this.$message(__("act_list_cant_not_unpub"));
          return row;
        }
      }
      const confirm = this.getConfirmStatus(row, do_publish);
      if (confirm) {
        const msg = this.getMessage(do_publish, row.language);
        const c = await this.confirmPromise(msg);
        if (c != "confirm") {
          return;
        }
      }
      const use_en = change_en_status ? +(do_publish && row.status != 3) : ""; // only unpublished lang can be published with en.
      const data = {
        status: change_en_status ? 1 : do_publish ? 3 : 4
      };
      // 发布部分美国英文时，partial_show_english = 1 && status= 5；
      // 取消发布部分美国英文时，partial_show_english = 0 && status= 1；
      // 不是部分美国英文发布类型 partial_show_english = ""
      let pub_with_ai = "";
      // 部分发布美国英文
      if (pubilsh_with_ai) {
        pub_with_ai = +(do_publish && row.status != 3); // 1 发布部分美国英文, 0 取消发布部分美国英文
        data.status = pub_with_ai === 1 ? 5 : 1;
      }
      let resp = await ADMIN_API.change_act_lang_status(
        this.id,
        row.language,
        use_en,
        data,
        pub_with_ai,
        { just_throw: false }
      );

      if (!resp.success) {
        await publishDraftMerchantActErrInterceptor(resp.error);

        await this.$msgbox({
          title: "[Backend Response]",
          message: resp.error?.message ?? "Error",
          showCancelButton: false,
          confirmButtonText: this.$t("global_confirm")
        });

        return row;
      }

      if (refresh) {
        // 重新获取数据
        let [
          {
            result: {
              status = [],
              source_language = "",
              is_support_published_with_ai = false
            }
          },
          translate_status_obj
        ] = await Promise.all([
          ADMIN_API.get_act_lang_status(this.id),
          getTranslateStatus(this.id)
        ]);
        status.forEach(v => {
          v.translation_status = translate_status_obj[v.language];
        });
        this.tableData = status;
        this.sourceLanguage = source_language;
        this.isSupportPublishedWithAi = is_support_published_with_ai;
        this.$message(__("global_success"));
        if (resp.result != "") {
          setTimeout(() => {
            this.$message({
              message: resp.result,
              duration: 5000
            });
          }, 1000);
        }
      }
    },
    jumpToSubmit(scope) {
      const actData = this.$root.actData;
      if (actData.sub_category_id === 2 && actData.had_been_inherit_spu) {
        const path = `mspa/experiencesadmincommon/aid/tours/seo`;
        const src = klook.strformat(
          `/{0}${path}/{1}?edit_status={2}&is_am={3}&lang=${
            scope.row.language
          }`,
          KLK_LANG_PATH,
          actData.activity_id,
          actData.status,
          false
        );
        window.open(src, "_blank");
        return;
      }

      window.location.href = `/${KLK_LANG_PATH}act/content/create/${
        this.id
      }?lang=${scope.row.language}`;
    },
    noEditLang(row) {
      return (
        // 这里限制 BD 不可以编辑其他英文
        !this.has_em_access &&
        row.language.includes("en") &&
        row.language !== "en_US"
      );
    },
    add(i, row) {
      this.add_flag = true;
      this.edit_lang = row.language;
    },
    edit(i, row) {
      this.add_flag = false;
      this.edit_lang = row.language;
    },
    refresh() {
      this.edit_lang = this.ref_lang = "";
    },
    async confirm() {
      if (this.add_flag) {
        await ADMIN_API.change_act_lang_status(this.id, this.edit_lang, "", {
          status: 1
        });
      }
      this.$root.confirm(this.edit_lang, this.ref_lang);
      this.dialogTableVisible = false;
    },
    cancel() {
      this.dialogTableVisible = false;
    },
    copyReady(row) {
      return row.language.match("en") && row.status > 1;
    },
    async copyEnglish(row) {
      this.copy_from = row.language;
      this.copyEnglishDialog = true;
      await new Promise((resolve, reject) =>
        this.$on("confirm-copy", ok => (ok ? resolve() : reject()))
      );
      await ADMIN_API.copy_multi_english({
        activity_id: this.id,
        data: {
          from: this.copy_from,
          target: this.copy_to
        }
      });
      klook.success();
      this.dialogTableVisible = false;
    },
    // 自定表头
    render_publish_with_ai_header(h, { column }) {
      return h("div", [
        h("span", column.label),
        h(
          "el-popover",
          {
            attrs: {
              placement: "right",
              content: this.$t("act_list_publish_with_AI_translation_intro"),
              trigger: "hover",
              width: "200"
            }
          },
          [
            h("i", {
              slot: "reference",
              class: "el-icon-information",
              style:
                "color: #888;margin-left: 5px;font-size: 14px;cursor:pointer;"
            })
          ]
        )
      ]);
    },
    getButtonText(text, status) {
      if (status === 5) {
        const s = this.$t("act_list_publishing_with_AI_translation_source", {
          language: this.sourceLanguage
        });
        return `${text} (${s})`;
      }
      return text;
    },
    // 状态展示
    getPublishStatusLabel(row) {
      if (row.default_show_english) {
        return this.$t("act_list_publishing_with_en_US");
      } else if (row.status === 5) {
        const ai = this.$t("act_list_publishing_with_AI_translation");
        const s = this.$t("act_list_publishing_with_AI_translation_source", {
          language: this.sourceLanguage
        });
        return `${ai} (${s})`;
      } else {
        return this.statusFmt(row.status);
      }
    },
    reviewEventLogs(row) {
      window.open(
        `/${KLK_LANG_PATH}act/activity/event_logs/${this.id}/?language=${
          row.language
        }`,
        "_blank"
      );
    }
  }
};
</script>

<style lang="scss">
.manage-lang {
  .text-wrap {
    word-break: break-word;
    white-space: pre-line;
    &.button {
      line-height: 18px;
    }
  }
  .el-dialog {
    max-height: calc(100vh - 15%);
    top: 10% !important;
  }
  &.is-edit {
    .el-dialog {
      min-width: 1150px;
    }
  }
  .header__note {
    position: sticky;
    top: 0;
    z-index: 999;
  }
  .header__button {
    position: sticky;
    top: 60px;
    margin-bottom: 5px;
    .el-button {
      margin-right: 8px;
    }
  }
  .el-table__body-wrapper {
    height: calc(100vh - 486px);
    overflow-x: hidden;
  }
  .el-dialog__body {
    padding-top: 10px;
  }
}
.ref {
  label {
    font-weight: normal;
    margin-bottom: 0;
  }
}
</style>

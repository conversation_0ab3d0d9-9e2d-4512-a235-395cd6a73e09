<template>
  <!-- TODO component, the popover filter table -->
  <div class="package-list-container">
    <header class="header--operation">
      <el-button type="text" @click="handleBackPackageList">
        {{ $t("archive_package_back") }}
      </el-button>

      <div v-if="modifiableHintVisible" class="warning">
        <p>
          <strong>{{ $t("act_modify_restrict_hint") }}</strong>
        </p>
      </div>
    </header>

    <div
      if="hasAmPermission || hasEmPermission"
      class="packages__table"
      :style="{
        '--offsetY': (modifiableHintVisible ? 58 : 0) + 'px'
      }"
    >
      <h3
        style="display: flex; justify-content: space-between; align-items: center;"
      >
        Archived Packages（{{ activity_id }} - {{ activityName }} ）
        <div>
          <router-link
            v-if="canSwitchList"
            :to="{
              name: $route.name,
              query: { role: isAmPage ? 'content' : 'am' }
            }"
            tag="label"
          >
            <el-button type="text" @click="handleRouterLink">
              {{
                isAmPage
                  ? $t("Go_To_Content_Package_List")
                  : $t("Go_To_AM_Package_List")
              }}
            </el-button>
          </router-link>

          <!-- <router-link
            if="hasAmPermission"
            :to="{
              name: 'pkg_change_pricing_model',
              params: {
                actId: activity_id
              },
              query: $route.query
            }"
            tag="label"
          >
            <el-button type="primary" @click="handleRouterLink">
              {{ $t("pm_change_pm") }}
            </el-button>
          </router-link> -->

          <el-tooltip effect="light" placement="top">
            <div slot="content">
              <p>Please go to onboarding backend to switch price model</p>
            </div>
            <span>
              <el-button disabled type="primary">
                {{ $t("pm_change_pm") }}
              </el-button>
            </span>
          </el-tooltip>

          <el-popover
            ref="popoverBD"
            placement="bottom-end"
            title=""
            width="360"
            trigger="hover"
          >
            <common-cb v-model="filterBdData" :items="filterBdColumns" />
          </el-popover>

          <el-button v-popover:popoverBD>
            <label class="glyphicon glyphicon-th icon-th"></label>
          </el-button>
        </div>
      </h3>
      <div style="font-size: 16px;margin-bottom: 10px;">
        {{ $t("package_list_archiv_package_tips") }}
      </div>
      <list-table :data="currentTableData" :columns="currentBdColumns" />
    </div>

    <tpl-manage-language
      ref="manageLanguage"
      v-model="manageLanguageProps.visible"
      v-bind="manageLanguageProps"
    />

    <tpl-pkg-lang-status-dialog
      v-model="pkgLangStatusDialog.visible"
      v-bind="pkgLangStatusDialog"
    />

    <tpl-batch-package-dialog
      v-model="batchPkg.visible"
      v-bind="batchPkg"
      @completed="initData"
    />

    <tpl-batch-unit-dialog
      v-model="batchUnit.visible"
      v-bind="batchUnit"
      @completed="initData"
    />
  </div>
</template>

<script>
import { CommonCb } from "../../../components/index";
import ListTable from "../../../components/table/listTable";
import { parseDotPrice, showDiscount } from "./utils";
import TplManageLanguage from "./components/tpl_manage_language";
import TplPkgLangStatusDialog from "./components/tpl_package_lang_status_dialog";
import TplBatchPackageDialog from "./components/tpl_batch_package_dialog";
import TplBatchUnitDialog from "./components/tpl_batch_unit_dialog";
import { checkPassAsync } from "../components/pass-standard-confirm";
const admin_const = require("admin_const");
const editlock = require("../../../pluginit/editlock.js");
const main = require("main");
main.ui.toggleSlider();

const bd_conf_mixin = {
  data() {
    return {
      filterBdData: []
    };
  },
  watch: {
    isAmPage: {
      immediate: true,
      handler() {
        let storage_col = window.localStorage.getItem(
          this.STORAGE_PACKAGE_LIST_FILTER_BD_COL
        );

        if (storage_col) {
          this.$set(this, "filterBdData", JSON.parse(storage_col));
        } else {
          this.$set(
            this,
            "filterBdData",
            this.bdColumns.map((item, index) => index)
          );
          window.localStorage.setItem(
            this.STORAGE_PACKAGE_LIST_FILTER_BD_COL,
            JSON.stringify(this.filterBdData)
          );
        }
      }
    },
    filterBdData: {
      deep: true,
      handler() {
        window.localStorage.setItem(
          this.STORAGE_PACKAGE_LIST_FILTER_BD_COL,
          JSON.stringify(this.filterBdData)
        );
      }
    }
  },
  computed: {
    STORAGE_PACKAGE_LIST_FILTER_BD_COL() {
      return `storage_package_list_filter_bd_col_${
        this.isAmPage ? "am" : "em"
      }`;
    },
    bdColumns() {
      return [
        {
          minWidth: "105px",
          label: this.isAmPage
            ? __("package_list_package_status")
            : __("package_list_status"),
          render: (h, params) => {
            // is_add_on
            if (params.row.attachedSku) return "";

            let { package_status, auto_published, auto_publish } = params.row;

            let auto = "";
            if (auto_published) {
              auto =
                auto_publish.published === 1
                  ? `<p>Auto Publish: ${auto_publish.published_time}</p><br>`
                  : `<p>Auto Unpublish: ${auto_publish.unpublished_time}</p>`;
            }

            return (
              <div>
                <span
                  style={{
                    color: package_status === 1 ? "#16aa77" : "#ff9d26"
                  }}
                >
                  {package_status === 1
                    ? this.$t("global_published")
                    : this.$t("global_unpublished")}
                </span>
                {auto}
                {this.isAmPage ? (
                  <a
                    className="el-button--text"
                    href={`/${window.KLK_LANG_PATH}act/activity/event_logs/${
                      this.activity_id
                    }?package_id=${params.row.package_id}`}
                  >
                    <br />
                    {this.$t("activity_event_logs")}
                  </a>
                ) : (
                  ""
                )}
              </div>
            );
          }
        },
        {
          minWidth: "80px",
          label: "ID",
          render: (h, params) => {
            if (params.row.attachedSku) return "";

            return <p>{params.row.package_id}</p>;
          }
        },
        {
          minWidth: "75px",
          label: __("package_order"),
          render: (h, params) => {
            if (params.row.attachedSku) return "";

            return (
              <div>
                <el-popover
                  ref="popoverPriority"
                  placement="top"
                  width="160"
                  value={params.row.visiblePriority}
                  onInput={v => {
                    if (v) {
                      params.row.tempPriority = "" + params.row.priority;
                    } else {
                      params.row.tempPriority = undefined;
                    }
                    params.row.visiblePriority = v; // model
                  }}
                >
                  <el-form
                    label-width="0px"
                    value={params.row}
                    nativeOn={{
                      // can use vOn:submit_native_prevent replace(https://github.com/vuejs/jsx), but need to deal with error which custom variable h
                      submit: event => {
                        event.preventDefault();
                        return false;
                      }
                    }}
                  >
                    <el-form-item
                      prop="priority"
                      rules={{
                        required: true,
                        validator: (rule, value, callback) =>
                          this.validatorPriority(
                            rule,
                            params.row.tempPriority,
                            callback
                          ),
                        trigger: "change,blur"
                      }}
                    >
                      <el-input
                        size="small"
                        clearable
                        value={
                          params.row.tempPriority === undefined
                            ? params.row.priority
                            : params.row.tempPriority
                        }
                        onInput={value => (params.row.tempPriority = value)}
                      />
                    </el-form-item>
                    <div style="text-align: right; margin: 0">
                      <el-button
                        size="mini"
                        type="text"
                        onClick={() => {
                          params.row.visiblePriority = false;
                        }}
                      >
                        {this.$t("global_button_cancel")}
                      </el-button>
                      <el-button
                        type="primary"
                        size="mini"
                        onClick={() => this.handleUpdatePriority(params.row)}
                      >
                        {this.$t("global_button_ok")}
                      </el-button>
                    </div>
                  </el-form>
                </el-popover>

                <p
                  style="display: inline-table; border-bottom: dashed 1px #0088cc;"
                  {...{
                    directives: [
                      {
                        name: "popover",
                        arg: "popoverPriority" // element-ui code
                      }
                    ]
                  }}
                >
                  {params.row.priority}
                </p>
              </div>
            );
          }
        },
        {
          minWidth: "150px",
          label: __("package_order_name"),
          render: (h, params) => {
            if (params.row.attachedSku) return "";

            return <p>{params.row.currentLocal.name}</p>;
          }
        },
        {
          minWidth: "100px",
          label: __("act_merchant"),
          render: (h, params) => {
            if (params.row.attachedSku) return "";

            let { merchant_id, merchant_name } = params.row.merchant_info;
            return <p>{`${merchant_id} - ${merchant_name}`}</p>;
          }
        },
        {
          minWidth: "165px",
          label: __("act_merchant_owner"),
          render: (h, params) => {
            if (params.row.attachedSku) return "";

            let { operational_db, commercial_db } = params.row.merchant_info;
            let o =
              (operational_db && `Responsible BD 2: ${operational_db}`) || "";
            let c =
              (commercial_db && `Responsible BD 1: ${commercial_db}`) || "";

            return (
              <p>
                {`${c}`}
                <br />
                {`${o}`}
              </p>
            );
          }
        },
        {
          minWidth: "130px",
          label: __("act_responsible_BD"),
          render: (h, params) => {
            if (params.row.attachedSku) return "";

            return <p>{this.activity_summary.responsible_bd}</p>;
          }
        },
        ...(this.isAmPage
          ? [
              {
                // am package table
                minWidth: "142px",
                label: __("package_matched_specs"),
                prop: "tips"
              },
              {
                minWidth: "120px",
                label: __("package_type"),
                render: (h, params) => {
                  // is_add_on
                  if (params.row.attachedSku) return "";

                  return (
                    <p>
                      {!params.row.main_package_id
                        ? this.$t("main_package")
                        : this.$t("merge_package")}
                    </p>
                  );
                }
              },
              {
                minWidth: "90px",
                label: __("package_list_modify_pkg"),
                render: (h, params) => {
                  if (params.row.attachedSku) return "";

                  return (
                    <div>
                      <el-button
                        type="text"
                        size="small"
                        onClick={() => this.handlerUnArchive(params.row)}
                      >
                        {this.$t("package_list_unarchive")}{" "}
                      </el-button>
                      <el-button
                        size="small"
                        type="text"
                        style={{
                          color: "#FF4949"
                        }}
                        onClick={() => this.handlerDel(params.row)}
                      >
                        {this.$t("global_delete")}
                      </el-button>
                    </div>
                  );
                }
              },
              {
                label: __("package_unit"),
                prop: "currentUnit.unit_name"
              },
              {
                label: __("pm_price_model"),
                render: (h, params) => {
                  if (params.row.package_pricing_model === 1) {
                    return <span>{this.$t("pm_price_calendar")}</span>;
                  } else {
                    return <span>{this.$t("pm_fixed_price")}</span>;
                  }
                }
              },
              {
                label: __("package_price_cost"),
                render: (h, params) => {
                  if (params.row.package_pricing_model === 1) {
                    return <span>-</span>;
                  }
                  let { cost, cost_currency } = params.row.currentUnit;

                  return (
                    <p
                      style={{
                        "word-break": "break-word",
                        color:
                          cost_currency !==
                          this.activity_summary.merchant_currency
                            ? "red"
                            : "inherit"
                      }}
                    >
                      {cost_currency} {parseDotPrice(cost, cost_currency)}
                    </p>
                  );
                }
              },
              ...(this.isTour
                ? []
                : [
                    {
                      minWidth: "85px",
                      label: __("package_price_retail"),
                      render: (h, params) => {
                        if (params.row.package_pricing_model === 1) {
                          return <span>-</span>;
                        }
                        return (
                          <p style="word-break: break-word;">
                            {params.row.sell_currency}{" "}
                            {params.row.currentUnit.retail}
                          </p>
                        );
                      }
                    }
                  ]),
              {
                minWidth: "85px",
                label: __("package_price_selling"),
                render: (h, params) => {
                  if (params.row.package_pricing_model === 1) {
                    return <span>-</span>;
                  }
                  return (
                    <p style="word-break: break-word;">
                      {params.row.sell_currency}{" "}
                      {params.row.currentUnit.selling}
                    </p>
                  );
                }
              },
              {
                minWidth: "150px",
                label: __("package_special_selling_price"),
                render: (h, params) => {
                  if (params.row.package_pricing_model === 1) {
                    return <span>-</span>;
                  }
                  let { custom } = params.row.currentUnit;
                  if (Array.isArray(custom) && custom.length) {
                    return (
                      <div style="word-break: break-word;">
                        {custom.map(item => {
                          if (item.type === 0)
                            return <p>{`${item.currency} ${item.amount}`}</p>;
                          return "";
                        })}
                      </div>
                    );
                  }
                  return "";
                }
              },
              {
                minWidth: "150px",
                label: __("same_day_booking_price"),
                render: (h, params) => {
                  let { custom } = params.row.currentUnit;
                  if (Array.isArray(custom) && custom.length) {
                    return (
                      <div>
                        {custom.map(item => {
                          if (item.type === 1)
                            return <p>{`${item.currency} ${item.amount}`}</p>;
                          return "";
                        })}
                      </div>
                    );
                  }
                  return "";
                }
              },
              {
                minWidth: "90px",
                label: __("package_discount"),
                render: (h, params) => {
                  let { selling, retail } = params.row.currentUnit;
                  return <p>{showDiscount(selling, retail)}</p>;
                }
              },
              {
                minWidth: "130px",
                label: __("package_price_regular_agent"),
                render: (h, params) => {
                  return (
                    <p style="word-break: break-word;">
                      {params.row.sell_currency}{" "}
                      {params.row.currentUnit.regular}
                    </p>
                  );
                }
              },
              {
                minWidth: "130px",
                label: __("package_price_premier_agent"),
                render: (h, params) => {
                  return (
                    <p style="word-break: break-word;">
                      {params.row.sell_currency}{" "}
                      {params.row.currentUnit.premier}
                    </p>
                  );
                }
              },
              {
                minWidth: "130px",
                label: __("package_india_agent_price"),
                render: (h, params) => {
                  return (
                    <p style="word-break: break-word;">
                      {params.row.sell_currency} {params.row.currentUnit.india}
                    </p>
                  );
                }
              },
              {
                minWidth: "160px",
                label: __("package_price_china_agent_rate"),
                render: (h, params) => {
                  let { china } = params.row.currentUnit;
                  return <p>{china > -1 ? `${china}%` : "-"}</p>;
                }
              },
              {
                minWidth: "105px",
                label: __("package_list_unit_status"),
                render: (h, params) => {
                  let { published } = params.row.currentUnit;

                  return (
                    <p>
                      {published
                        ? __("package_list_unit_published")
                        : __("package_list_unit_unpublished")}
                      <br />
                      <a
                        class="el-button--text"
                        href={`/${
                          window.KLK_LANG_PATH
                        }act/activity/event_logs/${
                          this.activity_id
                        }?package_id=${params.row.package_id}`}
                      >
                        {this.$t("activity_event_logs")}
                      </a>
                    </p>
                  );
                }
              }
            ]
          : [
              // em package table
              {
                minWidth: "90px",
                label: "Matched Specs",
                prop: "tips"
              },
              {
                minWidth: "175px",
                label: __("act_list_specs_status"),
                render: (h, params) => {
                  let color = ["#ff5722", "#02ac98", "#ffa628"][
                    params.row.spec_status
                  ];
                  let status = [
                    "act_em_specs_status_no",
                    "act_em_specs_status_matched",
                    "act_em_specs_status_pending"
                  ][params.row.spec_status];

                  return (
                    <span
                      style={{
                        color: color
                      }}
                    >
                      {this.$t(status)}
                    </span>
                  );
                }
              },
              {
                minWidth: "120px",
                label: __("package_type"),
                render: (h, params) => {
                  // is_add_on
                  if (params.row.attachedSku) return "";

                  return (
                    <p>
                      {!params.row.main_package_id
                        ? this.$t("main_package")
                        : this.$t("merge_package")}
                    </p>
                  );
                }
              },
              {
                label: __("package_unit"),
                prop: "currentUnit.unit_name"
              },
              {
                minWidth: "130px",
                // fixed: 'right',
                label: __("package_list_modify"),
                render: (h, params) => {
                  return (
                    <div style="display: flex; flex-direction: column; align-items: flex-start;">
                      <el-button
                        size="small"
                        type="text"
                        style={{
                          color: "#FF4949"
                        }}
                        onClick={() => this.handlerDel(params.row)}
                      >
                        {this.$t("global_delete")}
                      </el-button>
                      {this.canEditSpecs ? (
                        <el-button
                          size="small"
                          type="text"
                          onClick={() => this.handleEditSpecs(params.row)}
                        >
                          Edit Specs
                        </el-button>
                      ) : (
                        ""
                      )}
                    </div>
                  );
                }
              }
            ])
      ];
    },
    filterBdColumns() {
      return this.bdColumns.map((col, index) => {
        return {
          key: index,
          value: col.label
        };
      });
    },
    currentBdColumns() {
      return this.bdColumns.filter((item, index) =>
        this.filterBdData.includes(index)
      );
    }
  },
  methods: {
    handleRouterLink() {
      let scrollTop = document.documentElement.scrollTop;
      setTimeout(() => {
        document.documentElement.scrollTo(0, scrollTop);
        scrollTop = null;
      });
    }
  }
};

const batch_package_mixin = {
  data() {
    return {
      batchPkg: {
        visible: false,
        packages: [], // filter the package data before
        items: [], // filter the package data after
        toPublish: false
      }
    };
  },
  watch: {
    tableData: {
      deep: true,
      handler() {
        let packages = this.package_summary.map(item => {
          let name = _.find(item.package_local, {
            language_type: this.currentBLang
          }).name;

          if (!name) {
            name = _.find(item.package_local, {
              language_type: "en_US"
            }).name;
          }

          return {
            key: item.package_id,
            value: item.package_id + " - " + name,
            package_status: item.package_status // control btn
          };
        });

        this.$set(this.batchPkg, "packages", packages);
      }
    }
  },
  methods: {
    handleBatchPackage({ toPublish = true } = {}) {
      let items = this.batchPkg.packages.filter(
        item => !!item.package_status !== toPublish
      );

      this.$set(this, "batchPkg", {
        packages: this.batchPkg.packages,
        visible: true,
        items,
        toPublish
      });
    }
  }
};

const batch_unit_mixin = {
  data() {
    return {
      batchUnit: {
        visible: false,
        packages: [],
        toPublish: false,
        // control btn to display
        canPublish: false,
        canUnpublish: false
      }
    };
  },
  watch: {
    tableData: {
      deep: true,
      handler() {
        let packages = Object.values(
          this.tableData.reduce((acc, curr) => {
            if (!acc[curr.package_id]) {
              acc[curr.package_id] = {
                package_id: curr.package_id,
                package_name: curr.currentLocal.name,
                canPublishUnit: [],
                canUnpublishUnit: [],
                currentUnit: [],
                data: []
              };
            }

            let { sku_id, unit_name, published, sku_type } = curr.currentUnit;
            let cell = {
              key: sku_id,
              value: unit_name,
              published,
              sku_type // 0 无共享， 1 子sku, 2 主sku
            };

            if (published) {
              acc[curr.package_id].canUnpublishUnit.push(cell);
              this.batchUnit.canUnpublish = true;
            } else {
              acc[curr.package_id].canPublishUnit.push(cell);
              this.batchUnit.canPublish = true;
            }

            return acc;
          }, {})
        );

        this.$set(this.batchUnit, "packages", packages);
      }
    }
  },
  methods: {
    handleBatchUnit({ toPublish = true } = {}) {
      Object.assign(this.batchUnit, {
        visible: true,
        toPublish,
        packages: this.batchUnit.packages.map(item => ({
          ...item,
          currentUnit: toPublish ? item.canPublishUnit : item.canUnpublishUnit
        }))
      });
    }
  }
};

const search_mixins = {
  data() {
    return {
      search: {
        status: -1
      },
      statusOpts: [
        {
          label: __("all"),
          value: -1
        },
        {
          label: __("global_unpublished"),
          value: 0 // package_status: 0
        },
        {
          label: __("global_published"),
          value: 1 // package_status: 1
        }
      ],
      currentTableData: []
    };
  },
  watch: {
    tableData: {
      deep: true,
      immediate: true,
      handler() {
        this.getCurrentTableData();
      }
    },
    search: {
      deep: true,
      immediate: true,
      handler() {
        this.getCurrentTableData();
      }
    }
  },
  methods: {
    getCurrentTableData: _.debounce(function() {
      if (-1 === this.search.status) {
        this.$set(this, "currentTableData", this.tableData);
      } else {
        this.$set(
          this,
          "currentTableData",
          this.tableData.filter(
            item => item.package_status === this.search.status
          )
        );
      }
    }, 100)
  }
};

export default {
  name: "PackageList",
  components: {
    TplBatchUnitDialog,
    TplBatchPackageDialog,
    TplPkgLangStatusDialog,
    TplManageLanguage,
    ListTable,
    CommonCb
  },
  mixins: [bd_conf_mixin, batch_package_mixin, batch_unit_mixin, search_mixins],
  data() {
    return {
      modifiableHintVisible: false, // ?? Old codes directly display: none; no more associated codes.

      act_name: "",
      template_id: "",
      actPosition: {},
      tableData: [],
      activity_summary: {},
      package_summary: [],
      UNIT_PUBLISHED_NUM: {},

      manageLanguageProps: {
        activity_id: undefined,
        template_id: undefined,
        is_edit: true,
        url: "",
        visible: false
      },

      pkgLangStatusDialog: {
        visible: false,
        id: undefined
      },
      sub_category_id: ""
    };
  },
  computed: {
    activity_id() {
      return +this.$route.params.activity_id;
    },
    activityName() {
      return _.get(this.activity_summary, "currentLocal.title", "");
    },
    canEditSpecs() {
      return (
        window.KLK_PAGE_DATA._roles.canEnterCategoryAttributes &&
        !admin_const.spu_template_ids.includes(this.template_id)
      );
    },
    canAddNew() {
      return !(
        !this.actPosition.main_package ||
        this.actPosition.add_on_package === 1 ||
        admin_const.spu_template_ids.includes(this.template_id)
      );
    },
    currentBLang() {
      return lang_conf.getLangObj("F_LANG", "B_LANG")[window.KLK_LANG];
    },
    hasAmPermission() {
      return klook.userHasPermission(
        "act/activity_list_page/package_list_page/am_table"
      );
    },
    hasEmPermission() {
      return klook.userHasPermission(
        "act/activity_list_page/package_list_page/em_table"
      );
    },
    isActAdmin() {
      return klook.userHasPermission("act/activity_list_page/show_all_act");
    },
    isAmPage() {
      return this.hasAmPermission && this.$route.query.role !== "content";
    },
    canSwitchList() {
      return this.isActAdmin || (this.hasAmPermission && this.hasEmPermission);
    },
    hasGdsPermission() {
      return klook.userHasPermission(
        "act/activity_list_page/package_list_page/gds_table"
      );
    },
    hasGlobalGdsPermission() {
      return klook.userHasPermission(
        "act/activity_list_page/package_list_page/global_gds_price_edit"
      );
    },
    hasChinaAgentPermission() {
      return klook.userHasPermission(
        "act/activity_list_page/package_list_page/china_agent_price_edit"
      );
    },
    isTour() {
      // const sub_category_id = this.sub_category_id;
      // const ids = [2];
      // return ids.includes(sub_category_id);
      return false;
    }
  },
  beforeMount() {
    this.initData();
  },
  methods: {
    handleAddNew() {
      window.location.href = main.setQuery(
        {
          package_type: 1,
          activity_id: this.activity_id,
          package_id: "null",
          lang: "en_US"
        },
        main.setPathname(`/act/package/info`)
      );
    },
    async initData() {
      const res = await ADMIN_API.getActLang({
        activity_id: this.activity_id
      });
      this.template_id = res.result.template_id;
      this.sub_category_id = res.result.sub_category_id;

      this.actPosition = (await klook.pmsGet(
        klook.parseAjaxUrl(
          "/prosrv/activities/" + this.activity_id + "/position"
        )
      )).result;

      await this.getPrivsData();
    },
    async getPrivsData() {
      let resp = await ADMIN_API.getArchivedActPkgPrice({
        activity_id: this.activity_id,
        params: {
          custom_language: (await ADMIN_API.getActLang({
            activity_id: this.activity_id
          })).result.language //  use published language
        }
      });

      // // sort unit from published to unpublished
      // if (_.isEmpty(resp.result.package_summary)) {
      //   window.alert(
      //     "A package becomes editable in package list only after its schedule has been initialized. For now, we can't find any package editable here :("
      //   );
      //   history.back();
      //   klook.abort();
      // }

      this.$set(this, "activity_summary", {
        ...resp.result.activity_summary,
        currentLocal:
          _.find(resp.result.activity_summary.local, {
            language: this.currentBLang
          }) || {}
      });

      resp.result.package_summary = resp.result.package_summary.map(pkg => ({
        ...pkg,
        price: _.orderBy(pkg.price, "published", "desc")
      }));

      let tableData = resp.result.package_summary
        // sort package sequence with published first
        .filter(pkg => pkg.package_status === 1)
        .concat(
          resp.result.package_summary.filter(pkg => pkg.package_status !== 1)
        );

      this.package_summary = _.cloneDeep(
        tableData.filter(pkg =>
          pkg.price.some(sku => (sku.price_local || []).length)
        )
      );

      tableData.forEach(pkg => {
        pkg.price.forEach(unit => {
          if (unit.price_value.published) {
            if (typeof this.UNIT_PUBLISHED_NUM[pkg.package_id] === "number") {
              this.UNIT_PUBLISHED_NUM[pkg.package_id] += 1;
            } else {
              this.UNIT_PUBLISHED_NUM[pkg.package_id] = 1;
            }
          }
        });
      });

      this.$set(
        this,
        "tableData",
        tableData.reduce((acc, curr) => {
          // new wash data
          return [
            ...acc,
            ...curr.price
              .filter(sku => (sku.price_local || []).length) // why hide the package which is no sku
              .map((sku, index) => {
                return {
                  ...curr,
                  attachedSku: index !== 0,
                  currentLocal: _.find(curr.package_local, {
                    language_type: this.currentBLang
                  }),
                  currentUnit: {
                    ...sku.price_value,
                    ...(_.find(sku.price_local, {
                      language_type: this.currentBLang
                    }) || {})
                  },
                  // for jsx
                  visiblePriority: false
                };
              })
          ];
        }, [])
      );
    },
    handleBackPackageList() {
      this.$router.push({
        name: "package_list",
        query: this.$route.query
      });
    },
    handleEditPrice(data, role = "gds") {
      let search = new URLSearchParams(location.search);
      search.set("sku_id", data.currentUnit.sku_id);

      window.location.href = `/${KLK_LANG_PATH}act/activity/${
        this.activity_id
      }/price/${data.package_id}/modify_${role}?${search}`;
    },
    handleEditSpecs() {
      window.location.href = `/${KLK_LANG_PATH}act/${
        this.activity_id
      }/specs/list`;
    },
    async handelEditPackage(row) {
      if (this.hasAmPermission) {
        try {
          await editlock.checkLock(this.activity_id, false, null, {
            user_type: 0,
            language: "ALL"
          });
        } catch (error) {
          // 如果活动已迁移，checkLock 会抛出 ACTIVITY_MIGRATED 异常并重定向
          if (error.message === 'ACTIVITY_MIGRATED') {
            return;
          }
          throw error;
        }
      }

      let url = `/${window.KLK_LANG_PATH}act/package/info?activity_id=${
        this.activity_id
      }&package_id=${row.package_id}&package_type=1&lang=en_US`;

      this.$set(this, "manageLanguageProps", {
        activity_id: this.activity_id,
        template_id: this.template_id,
        url,
        visible: true
      });
    },
    // @type: Boolean, true: to publish
    async handleRoleOperator(row, type) {
      let popupStr = klook.strformat(
        __("confirm_publish_pkg"),
        type ? __("global_publish_1") : __("global_unpublish_1"),
        row.package_id
      );
      // generate specific popup string base on status of auto_publish
      let auto_published_str = "";
      let time_str = "";
      let publish_str = "";
      if (type && row.auto_publish.published) {
        auto_published_str = "auto-published";
        time_str = row.auto_publish.published_time;
        publish_str = "publish";
        popupStr = `The package has set to be ${auto_published_str} by ${time_str}, are you sure to ${publish_str} it now?`;
      }
      if (!type && row.auto_publish.unpublished) {
        auto_published_str = "auto-unpublished";
        time_str = row.auto_publish.unpublished_time;
        publish_str = "unpublish";
        popupStr = `The package has set to be ${auto_published_str} by ${time_str}, are you sure to ${publish_str} it now?`;
      }

      await this.$confirm(popupStr);
      if (!type) {
        // published to unpublish
        await main.checkActivityInfo(row.package_id, "package");
      }

      let resp = await ADMIN_API.pkgPublished({
        data: {
          package_id: row.package_id,
          published: type
        },
        just_throw: false
      });
      if (resp.success) {
        this.$message(this.$t("global_success"));
      } else {
        await this.$alert(resp.error.message);
      }
      await this.getPrivsData();
    },
    async handlerDel(row) {
      await this.$confirm(
        `${this.$t("package_list_delete_package_tips")} id: ${row.package_id}`
      );
      await ADMIN_API.pkgDestroy({
        package_id: row.package_id
      });
      this.$message(this.$t("global_success"));
      await this.getPrivsData();
    },
    async handlerArchive(row) {
      await this.$confirm(
        `${this.$t("js_confirm_archive")} id: ${row.package_id}`
      );
      await ADMIN_API.pkgArchive({
        package_id: row.package_id
      });
      this.$message(this.$t("global_success"));
      await this.getPrivsData();
    },
    async handlerUnArchive(row) {
      await this.$confirm(
        `${this.$t("js_confirm_unarchive")} id: ${row.package_id}`
      );
      await ADMIN_API.pkgUnArchive({
        package_id: row.package_id
      });
      this.$message(this.$t("global_success"));
      await this.getPrivsData();
    },
    validatorPriority(rule, value, callback) {
      if (value && !value.trim()) {
        callback(new Error(this.$t("global_validate_text_required")));
      } else if (!/^\d+$/.test(value)) {
        callback(new Error(this.$t("global_validate_number_required")));
      } else {
        callback();
      }
    },
    async handleUpdatePriority(row) {
      await ADMIN_API.updatePackageSort(
        this.tableData.reduce((acc, curr) => {
          if (curr.attachedSku) return acc;

          return [
            ...acc,
            {
              package_id: curr.package_id,
              priority:
                row.package_id === curr.package_id
                  ? +row.tempPriority
                  : curr.priority
            }
          ];
        }, [])
      );

      this.$message(this.$t("global_success"));
      await this.getPrivsData();
    },
    handlePackageListManage(row) {
      this.$set(this, "pkgLangStatusDialog", {
        visible: true,
        id: row.package_id
      });
    },
    handleUnitPublish(row) {
      let { sku_id, published } = row.currentUnit;
      let doPublish = !published;

      if (doPublish) {
        // Do publish
        if (this.UNIT_PUBLISHED_NUM[row.package_id] > Number.MAX_SAFE_INTEGER) {
          this.$alert(this.$t("package_price_units_overflow"));
          return false;
        }
      }

      let _resultPromise = doPublish
        ? Promise.resolve()
        : main.checkActivityInfo(sku_id, "sku");

      _resultPromise
        .then(async () => {
          let checkPassRes = {};
          if (!doPublish) {
            // 下架活动、套餐、sku的时候需要检查是否是 pass standard
            checkPassRes = await checkPassAsync({ sku_ids: sku_id });

            if (checkPassRes && checkPassRes.stop) {
              return;
            }
          }
          let resp = await ADMIN_API.pkgSkuPublish({
            data: {
              sku_id: sku_id,
              published: doPublish,
              reason: checkPassRes.reason,
              reason_code: checkPassRes.reasonCode,
              force: !!checkPassRes.force
            },
            just_throw: false
          });

          if (resp.success) {
            this.$message(this.$t("global_success"));
            await this.getPrivsData();
          } else {
            this.$alert(
              (resp.error && resp.error.message) || this.$t("global_error")
            );
          }
        })
        .catch(err => {
          console.log(err);
          this.$alert(err);
        });
    }
  }
};
</script>

<style lang="scss">
$stickyTop: calc(158px + var(--offsetY));

.package-list-container {
  .header--operation {
    position: sticky;
    top: 35px;
    z-index: 99;
    background-color: #fff;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
  }
  .el-table {
    font-size: 12px;
    .el-button + .el-button {
      margin-left: 0;
    }
  }
  .el-table th > .cell {
    line-height: 2;
    word-break: break-word;
  }
  .el-table__fixed {
    z-index: 10;
    overflow: inherit;
    .el-table__fixed-header-wrapper {
      position: sticky;
      top: $stickyTop;
      z-index: 11;
    }
    .el-table__fixed-header-wrapper,
    .el-table__fixed-body-wrapper {
      width: 100%;
      overflow: hidden;
    }
  }
  .el-table p {
    margin: 0;
  }
  .packages__table {
    position: relative;
    &,
    .el-table {
      overflow: inherit;
    }
    .el-table__header-wrapper {
      position: sticky;
      top: $stickyTop;
      z-index: 9;
    }
  }
}
</style>

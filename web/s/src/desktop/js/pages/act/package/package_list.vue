<template>
  <!-- TODO component, the popover filter table -->
  <div class="package-list-container">
    <header class="header--operation">
      <el-button type="text" @click="handleBackActivityList">
        {{ $t("package_back_act") }}
      </el-button>

      <div v-if="modifiableHintVisible" class="warning">
        <p>
          <strong>{{ $t("act_modify_restrict_hint") }}</strong>
        </p>
      </div>

      <div>
        <div style="margin-bottom: 10px">
          <label style="margin-right: 10px">{{
            $t("global_publish_status")
          }}</label>
          <el-select v-model="search.status" size="small">
            <el-option
              v-for="item in statusOpts"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>

        <el-button v-if="canAddNew" size="small" @click="handleAddNew">
          {{ $t("package_list_add_new") }}
        </el-button>

        <el-button
          v-if="publishPkgList.length"
          type="primary"
          size="small"
          @click="handleBatchPackage()"
        >
          Batch Package Publish
        </el-button>
        <el-button
          v-if="unpublishPkgList.length"
          type="primary"
          size="small"
          @click="
            handleBatchPackage({
              toPublish: false
            })
          "
        >
          Batch Package Unpublish
        </el-button>

        <el-button
          v-if="batchUnit.canPublish"
          type="primary"
          size="small"
          @click="handleBatchUnit()"
        >
          Batch Unit Publish
        </el-button>
        <el-button
          v-if="batchUnit.canUnpublish"
          type="primary"
          size="small"
          @click="
            handleBatchUnit({
              toPublish: false
            })
          "
        >
          Batch Unit Unpublish
        </el-button>
        <el-button type="primary" size="small" @click="toSetting">
          Package publish restriction setting
        </el-button>
      </div>
    </header>

    <div
      v-if="hasGdsPermission"
      class="packages__table"
      :style="{
        '--offsetY': (modifiableHintVisible ? 58 : 0) + 'px'
      }"
    >
      <h3
        style="
          display: flex;
          justify-content: space-between;
          align-items: center;
        "
      >
        {{ activityName }} : (For GDS)
        <div>
          <el-popover
            ref="popoverGDS"
            placement="bottom-end"
            title=""
            width="360"
            trigger="hover"
          >
            <common-cb v-model="filterGdsData" :items="filterGDSColumns" />
          </el-popover>

          <el-button v-popover:popoverGDS>
            <label class="glyphicon glyphicon-th icon-th"></label>
          </el-button>
        </div>
      </h3>
      <list-table
        ref="gdsTable"
        :data="currentTableData"
        :columns="currentGDSColumns"
      />
    </div>

    <br />
    <br />

    <div
      v-if="hasAmPermission || hasEmPermission"
      class="packages__table"
      :style="{
        '--offsetY': (modifiableHintVisible ? 58 : 0) + 'px'
      }"
    >
      <div
        class="el-alert pkg-list-tip el-alert--warning"
        v-if="promoTipData.is_price_engine"
      >
        <i class="el-alert__icon el-icon-warning"></i>
        <div class="el-alert__content">
          <span class="el-alert__title" v-html="$t('package_list_tip1')"></span>
        </div>
      </div>

      <div
        class="el-alert pkg-list-tip el-alert--warning"
        v-if="promoTipData.is_promotion"
      >
        <i class="el-alert__icon el-icon-warning"></i>
        <div class="el-alert__content">
          <span
            class="el-alert__title"
            v-html="
              $t('package_list_tip2', {
                '0': promoTipData.promotion_event_name.join(',')
              })
            "
          ></span>
        </div>
      </div>

      <h3
        style="
          display: flex;
          justify-content: space-between;
          align-items: center;
        "
      >
        {{ activityName }} : (For
        {{ isAmPage ? "Activity" : "Content" }} Manager)
        <div>
          <el-button type="text" @click="handleGoToArchivePackageList">
            {{ $t("package_list_archived_packages") }} </el-button
          >&nbsp;&nbsp;&nbsp;&nbsp;
          <router-link
            v-if="canSwitchList"
            :to="{
              name: $route.name,
              query: { role: isAmPage ? 'content' : 'am' }
            }"
            tag="label"
          >
            <el-button type="text" @click="handleRouterLink">
              {{
                isAmPage
                  ? $t("Go_To_Content_Package_List")
                  : $t("Go_To_AM_Package_List")
              }}
            </el-button>
          </router-link>

          <!-- <router-link
            if="hasAmPermission"
            :to="{
              name: 'pkg_change_pricing_model',
              params: {
                actId: activity_id
              },
              query: $route.query
            }"
            tag="label"
          >
            <el-button disabled type="primary" @click="handleRouterLink">
              {{ $t("pm_change_pm") }}
            </el-button>
          </router-link> -->

          <el-tooltip effect="light" placement="top">
            <div slot="content">
              <p>Please go to onboarding backend to switch price model</p>
            </div>
            <span>
              <el-button disabled type="primary">
                {{ $t("pm_change_pm") }}
              </el-button>
            </span>
          </el-tooltip>

          <el-popover
            ref="popoverBD"
            placement="bottom-end"
            title=""
            width="360"
            trigger="hover"
          >
            <common-cb v-model="filterBdData" :items="filterBdColumns" />
          </el-popover>

          <el-button v-popover:popoverBD>
            <label class="glyphicon glyphicon-th icon-th"></label>
          </el-button>
        </div>
      </h3>
      <list-table
        ref="bdTable"
        :key="dragedTableData"
        class="package-list-bd"
        :data="currentTableData"
        :columns="currentBdColumns"
      />
    </div>

    <tpl-manage-language
      ref="manageLanguage"
      v-model="manageLanguageProps.visible"
      v-bind="manageLanguageProps"
    />

    <tpl-pkg-lang-status-dialog
      v-model="pkgLangStatusDialog.visible"
      v-bind="pkgLangStatusDialog"
    />

    <tpl-batch-package-dialog
      v-model="batchPkg.visible"
      v-bind="batchPkg"
      @completed="initData"
    />

    <tpl-batch-unit-dialog
      v-model="batchUnit.visible"
      v-bind="batchUnit"
      @completed="initData"
    />

    <suspend-dialog
      v-if="suspend.visible"
      ref="SuspendDialog"
      v-bind="suspend"
      :visible.sync="suspend.visible"
      :title="$t('79632')"
      type="pkg"
      @submit="suspend.handleSuspendSubmit"
    />
  </div>
</template>

<script>
import { CommonCb } from "../../../components/index";
import ListTable from "../../../components/table/listTable";
import { parseDotPrice, showDiscount } from "./utils";
import TplManageLanguage from "./components/tpl_manage_language";
import TplPkgLangStatusDialog from "./components/tpl_package_lang_status_dialog";
import TplBatchPackageDialog from "./components/tpl_batch_package_dialog";
import TplBatchUnitDialog from "./components/tpl_batch_unit_dialog";
import SuspendDialog from "../activity/components/suspendDialog";
import { checkPassAsync } from "../components/pass-standard-confirm";
const admin_const = require("admin_const");
const editlock = require("../../../pluginit/editlock.js");
const main = require("main");
const Sortable = require("sortablejs");
main.ui.toggleSlider();

const gds_conf_mixin = {
  data() {
    return {
      dragedTableData: 1,
      filterGdsData: [],
      agentLevelData: [],
      skuPriceList: [],
      promoTipData: {},
      STORAGE_PACKAGE_LIST_FILTER_GDS_COL: "storage_package_list_filter_gds_col"
    };
  },
  methods: {
    loadAgentLevel() {
      klook
        .pmsGet(klook.parseAjaxUrl("/agent/adminserv/agent/level/query"), {
          page: "1",
          limit: "10000"
        })
        .then(resp => {
          if (resp.success && resp.result) {
            this.agentLevelData.splice(
              0,
              0,
              ...(resp.result.agent_level_infos || [])
            );
          } else {
            klook.error(resp.error && resp.error.message);
          }
        });
    },
    loadSkuPrice() {
      const category = klook.userHasPermission(
        "act/activity_list_page/package_list_page/china_agent_price_edit"
      )
        ? "China"
        : "Global";
      klook
        .pmsGet(
          klook.parseAjaxUrl("/agent/adminserv/pricing/sku/price/alllevel"),
          {
            sale_from: "default",
            activity_id: this.$route.params.activity_id,
            category
          }
        )
        .then(resp => {
          if (resp.success && resp.result) {
            this.skuPriceList.splice(0, 0, ...resp.result);
          } else {
            klook.error(resp.error && resp.error.message);
          }
        });
    },
    loadPromoTips() {
      klook
        .pmsGet(klook.parseAjaxUrl("/prosrv/activities/get_price_influence"), {
          activity_id: this.$route.params.activity_id
        })
        .then(resp => {
          if (resp.success && resp.result) {
            this.promoTipData = resp.result;
          }
        });
    }
  },
  created() {
    const hasGdsPermission = klook.userHasPermission(
      "act/activity_list_page/package_list_page/gds_table"
    );
    if (hasGdsPermission) {
      this.loadAgentLevel();
      this.loadSkuPrice();
    }
  },
  beforeMount() {
    window.s = this;
    let storage_col = window.localStorage.getItem(
      this.STORAGE_PACKAGE_LIST_FILTER_GDS_COL
    );

    if (storage_col) {
      this.$set(this, "filterGdsData", JSON.parse(storage_col));
    } else {
      this.$set(
        this,
        "filterGdsData",
        this.bdColumns.map((item, index) => index)
      );
      window.localStorage.setItem(
        this.STORAGE_PACKAGE_LIST_FILTER_GDS_COL,
        JSON.stringify(this.filterGdsData)
      );
    }
  },
  watch: {
    filterGdsData: {
      deep: true,
      handler() {
        window.localStorage.setItem(
          this.STORAGE_PACKAGE_LIST_FILTER_GDS_COL,
          JSON.stringify(this.filterGdsData)
        );
      }
    }
  },
  computed: {
    newSysURL() {
      return `/mspa/partnership/agent/activity_edit?activity_id=${
        this.activity_id
      }`;
    },
    gdsColumns() {
      // const priceCols = this.agentLevelData.map(level => {
      //   return {
      //     minWidth: "140px",
      //     label: level.level_name,
      //     render: (h, params) => {
      //       let { sku_id, cost, cost_currency } = params.row.currentUnit;
      //       const markupRate = (
      //         this.skuPriceList.find(
      //           item => item.agent_level === level.id && sku_id === item.sku_id
      //         ) || {}
      //       ).markup_rate;
      //       if (markupRate === undefined) return "";
      //       const price = parseFloat(cost * (1 + markupRate / 100)).toFixed(2);
      //       return h("div", [
      //         h("p", `${cost_currency} ${price}`),
      //         h("p", `${markupRate}%`)
      //       ]);
      //     }
      //   };
      // });
      return [
        {
          minWidth: "130px",
          label: __("package_list_package_status"),
          render: (h, params) => {
            if (params.row.attachedSku) return "";

            const statusDict = {
              1: { color: "#16aa77", text: this.$t("global_published") },
              0: { color: "#ff9d26", text: this.$t("global_unpublished") },
              2: { color: "#FF5B00", text: this.$t("79630") },
              3: { color: "#FF5B00", text: this.$t("79630") }
            };
            const { package_status } = params.row;
            const curr = statusDict[package_status];

            let withWarmUpHTML = "";
            if (package_status === 3) {
              withWarmUpHTML = (
                <span style="margin-left: 4px; color: #444;">
                  {this.$t("79648")}
                  <i
                    v-tooltip={{
                      content: this.$t("79649"),
                      visible: true,
                      placement: "right"
                    }}
                    style="margin-left: 4px; font-size: 9px;"
                    class="el-icon-warning"
                  />
                </span>
              );
            }

            return (
              <p
                style={{
                  color: curr?.color ?? "#16aa77"
                }}
              >
                {curr?.text ?? ""}
                {withWarmUpHTML}
              </p>
            );
          }
        },
        {
          minWidth: "80px",
          label: "ID",
          render: (h, params) => {
            if (params.row.attachedSku) return "";

            return <p>{params.row.package_id}</p>;
          }
        },
        {
          minWidth: "150px",
          label: __("package_order_name"),
          render: (h, params) => {
            if (params.row.attachedSku) return "";

            return <p>{params.row.currentLocal.name}</p>;
          }
        },
        {
          minWidth: "120px",
          label: __("package_type"),
          render: (h, params) => {
            if (params.row.attachedSku) return "";

            return (
              <p>
                {!params.row.main_package_id
                  ? __("main_package")
                  : __("merge_package")}
              </p>
            );
          }
        },
        {
          minWidth: "150px",
          label: __("package_unit"),
          prop: "currentUnit.unit_name"
        },
        {
          minWidth: "85px",
          label: __("package_price_cost"),
          render: (h, params) => {
            if (params.row.package_pricing_model === 1) {
              return <span>-</span>;
            } else {
              let { cost, cost_currency } = params.row.currentUnit;
              return <p>{parseDotPrice(cost, cost_currency)}</p>;
            }
          }
        },
        {
          minWidth: "85px",
          label: __("package_price_selling"),
          render: (h, params) => {
            if (params.row.package_pricing_model === 1) {
              return <span>-</span>;
            } else {
              return (
                <p style="word-break: break-word;">
                  {params.row.sell_currency} {params.row.currentUnit.selling}
                </p>
              );
            }
          }
        },
        {
          minWidth: "105px",
          label: __("package_list_unit_status"),
          render: (h, params) => {
            let { published } = params.row.currentUnit;
            return (
              <p>
                {published
                  ? __("package_list_unit_published")
                  : __("package_list_unit_unpublished")}
              </p>
            );
          }
        },
        {
          minWidth: "110px",
          label: __("package_list_modify_unit"),
          render: (h, params) => {
            return (
              <el-button
                type="text"
                size="small"
                onClick={() => {
                  window.location.href = this.newSysURL;
                }}
              >
                {this.$t("package_edit_price")}
              </el-button>
            );
          }
        }
      ];
    },
    filterGDSColumns() {
      return this.gdsColumns.map((col, index) => {
        return {
          key: index,
          value: col.label
        };
      });
    },
    currentGDSColumns() {
      return this.gdsColumns.filter((item, index) =>
        this.filterGdsData.includes(index)
      );
    }
  }
};

const bd_conf_mixin = {
  data() {
    return {
      filterBdData: []
    };
  },
  watch: {
    isAmPage: {
      immediate: true,
      handler() {
        let storage_col = window.localStorage.getItem(
          this.STORAGE_PACKAGE_LIST_FILTER_BD_COL
        );

        if (storage_col) {
          this.$set(this, "filterBdData", JSON.parse(storage_col));
        } else {
          this.$set(
            this,
            "filterBdData",
            this.bdColumns.map((item, index) => index)
          );
          window.localStorage.setItem(
            this.STORAGE_PACKAGE_LIST_FILTER_BD_COL,
            JSON.stringify(this.filterBdData)
          );
        }

        if (this.isAmPage && _.isEqual(this.promoTipData, {})) {
          this.loadPromoTips();
        }
      }
    },
    filterBdData: {
      deep: true,
      handler() {
        window.localStorage.setItem(
          this.STORAGE_PACKAGE_LIST_FILTER_BD_COL,
          JSON.stringify(this.filterBdData)
        );
      }
    }
  },
  computed: {
    STORAGE_PACKAGE_LIST_FILTER_BD_COL() {
      return `storage_package_list_filter_bd_col_${
        this.isAmPage ? "am" : "em"
      }`;
    },
    bdColumns() {
      return [
        {
          minWidth: "50px",
          label: "Drag",
          render: (h, params) => {
            if (params.row.attachedSku)
              return <div class="drag-disable-item" />;
            return (
              <div class="drag-to-order">
                <div style="cursor: move; color: #1079fb;" />
                <img
                  class="drag_img"
                  src="https://cdn.klook.com/s/dist_web/klook-admin-projects/klook-admin/s/dist/desktop/imgs/ordertool.png"
                />
              </div>
            );
          }
        },

        {
          minWidth: "75px",
          label: __("package_order"),
          render: (h, params) => {
            if (params.row.attachedSku) return "";

            return (
              <div>
                <el-popover
                  ref="popoverPriority"
                  placement="top"
                  width="160"
                  value={params.row.visiblePriority}
                  onInput={v => {
                    if (v) {
                      params.row.tempPriority = "" + params.row.priority;
                    } else {
                      params.row.tempPriority = undefined;
                    }
                    params.row.visiblePriority = v; // model
                  }}
                >
                  <el-form
                    label-width="0px"
                    value={params.row}
                    nativeOn={{
                      // can use vOn:submit_native_prevent replace(https://github.com/vuejs/jsx), but need to deal with error which custom variable h
                      submit: event => {
                        event.preventDefault();
                        return false;
                      }
                    }}
                  >
                    <el-form-item
                      prop="priority"
                      rules={{
                        required: true,
                        validator: (rule, value, callback) =>
                          this.validatorPriority(
                            rule,
                            params.row.tempPriority,
                            callback
                          ),
                        trigger: "change,blur"
                      }}
                    >
                      <el-input
                        size="small"
                        clearable
                        value={
                          params.row.tempPriority === undefined
                            ? params.row.priority
                            : params.row.tempPriority
                        }
                        onInput={value => (params.row.tempPriority = value)}
                      />
                    </el-form-item>
                    <div style="text-align: right; margin: 0">
                      <el-button
                        size="mini"
                        type="text"
                        onClick={() => {
                          params.row.visiblePriority = false;
                        }}
                      >
                        {this.$t("global_button_cancel")}
                      </el-button>
                      <el-button
                        type="primary"
                        size="mini"
                        onClick={() => this.handleUpdatePriority(params.row)}
                      >
                        {this.$t("global_button_ok")}
                      </el-button>
                    </div>
                  </el-form>
                </el-popover>

                <p
                  style="display: inline-table; border-bottom: dashed 1px #0088cc;"
                  {...{
                    directives: [
                      {
                        name: "popover",
                        arg: "popoverPriority" // element-ui code
                      }
                    ]
                  }}
                >
                  {params.row.priority}
                </p>
              </div>
            );
          }
        },
        {
          minWidth: "130px",
          label: this.isAmPage
            ? __("package_list_package_status")
            : __("package_list_status"),
          render: (h, params) => {
            // PackageStatusInvalid             int64 = -1 // 无效状态
            // PackageStatusUnpublished         int64 = 0  // 未发布
            // PackageStatusPublished           int64 = 1  // 已发布
            // PackageStatusSuspended           int64 = 2  // 暂停销售
            // PackageStatusTocSuspendWarmingUp int64 = 3  // 暂停销售 (预热中)
            // is_add_on
            if (params.row.attachedSku) return "";

            let { package_status, auto_published, auto_publish } = params.row;

            let auto = "";
            if (auto_published) {
              let text = "";
              switch (package_status) {
                case 0: {
                  text = `Auto Unpublish: ${auto_publish.unpublished_time}`;
                  break;
                }
                case 1: {
                  text = `Auto Publish: ${auto_publish.published_time}`;
                  break;
                }
                case 2:
                case 3: {
                  let {
                    suspended_start_time,
                    suspended_end_time,
                    reason
                  } = auto_publish.suspended_config;
                  if (package_status === 3) {
                    text = `Auto Publish: ${auto_publish.published_time}\n`;
                  }
                  if (suspended_start_time) {
                    text += `Auto suspend start time: ${suspended_start_time}\n`;
                  }
                  if (suspended_end_time) {
                    text += `Auto suspend end time: ${suspended_end_time}\n`;
                  }
                  if (reason) {
                    text += `Reason: ${reason}\n`;
                  }
                  break;
                }
              }
              console.log(text);
              auto = (
                <p style="white-space: break-spaces;">
                  <br />
                  {text}
                  <br />
                </p>
              );
            }

            const statusDict = {
              1: { color: "#16aa77", text: this.$t("global_published") },
              0: { color: "#ff9d26", text: this.$t("global_unpublished") },
              2: { color: "#FF5B00", text: this.$t("79630") },
              3: { color: "#FF5B00", text: this.$t("79630") }
            };

            let withWarmUpHTML = "";
            if (package_status === 3) {
              withWarmUpHTML = (
                <span style="margin-left: 4px; color: #444;">
                  {this.$t("79648")}
                  <i
                    v-tooltip={{
                      content: this.$t("79649"),
                      visible: true,
                      placement: "right"
                    }}
                    style="margin-left: 4px; font-size: 9px;"
                    class="el-icon-warning"
                  />
                </span>
              );
            }

            return (
              <div>
                <span
                  style={{
                    color: statusDict?.[package_status]?.color ?? "#ff9d26"
                  }}
                >
                  {statusDict?.[package_status]?.text}
                  {withWarmUpHTML}
                </span>
                {auto}
                {this.isAmPage ? (
                  <a
                    className="el-button--text"
                    href={`/${window.KLK_LANG_PATH}act/activity/event_logs/${
                      this.activity_id
                    }?package_id=${params.row.package_id}`}
                  >
                    <br />
                    {this.$t("activity_event_logs")}
                  </a>
                ) : (
                  ""
                )}
              </div>
            );
          }
        },
        {
          minWidth: "80px",
          label: "ID",
          render: (h, params) => {
            if (params.row.attachedSku) return "";

            return <p>{params.row.package_id}</p>;
          }
        },
        {
          minWidth: "150px",
          label: __("package_order_name"),
          render: (h, params) => {
            if (params.row.attachedSku) return "";

            return <p>{params.row.currentLocal.name}</p>;
          }
        },
        {
          minWidth: "100px",
          label: __("act_merchant"),
          render: (h, params) => {
            if (params.row.attachedSku) return "";

            let { merchant_id, merchant_name } = params.row.merchant_info;
            return <p>{`${merchant_id} - ${merchant_name}`}</p>;
          }
        },
        {
          minWidth: "165px",
          label: __("act_merchant_owner"),
          render: (h, params) => {
            if (params.row.attachedSku) return "";

            let { operational_db, commercial_db } = params.row.merchant_info;
            let o =
              (operational_db && `Responsible BD 2: ${operational_db}`) || "";
            let c =
              (commercial_db && `Responsible BD 1: ${commercial_db}`) || "";

            return (
              <p>
                {`${c}`}
                <br />
                {`${o}`}
              </p>
            );
          }
        },
        {
          minWidth: "130px",
          label: __("act_responsible_BD"),
          render: (h, params) => {
            if (params.row.attachedSku) return "";

            return <p>{this.activity_summary.responsible_bd}</p>;
          }
        },
        ...(this.isAmPage
          ? [
              {
                // am package table
                minWidth: "142px",
                label: __("package_matched_specs"),
                prop: "tips"
              },
              {
                minWidth: "120px",
                label: __("package_type"),
                render: (h, params) => {
                  // is_add_on
                  if (params.row.attachedSku) return "";

                  return (
                    <p>
                      {!params.row.main_package_id
                        ? this.$t("main_package")
                        : this.$t("merge_package")}
                    </p>
                  );
                }
              },
              {
                minWidth: "90px",
                label: __("package_timeslot_list"),
                render: (h, params) => {
                  if (params.row.open_ticket) {
                    return this.$t("package_open_ticket");
                  }

                  let href = "";
                  if (this.isSpuPackage(params.row)) {
                    href = `/mspa/experiencesadmincommon/aid/tours/inventory-schedule/${
                      this.activity_id
                    }?ref=&package_id=${params.row.package_id}&lang=en_US`;
                  } else {
                    href = `/${
                      window.KLK_LANG_PATH
                    }act/package/schedule?activity_id=${
                      this.activity_id
                    }&lang=en_US&package_id=${params.row.package_id}`;
                  }

                  return (
                    <a class="el-button--text" href={href}>
                      {this.$t("package_list_manage")}
                    </a>
                  );
                }
              },
              {
                minWidth: "75px",
                label: "Preview Tag",
                render: (h, params) => {
                  if (params.row.attachedSku) return "";
                  return (
                    <div>
                      <el-checkbox
                        true-label={1}
                        false-label={0}
                        value={params.row.package_preview_tag}
                        onInput={value =>
                          (params.row.package_preview_tag = value)
                        }
                        onchange={() =>
                          this.updatePackagePreviewTag(params.row)
                        }
                      />
                    </div>
                  );
                }
              },
              {
                minWidth: "130px",
                label: __("package_list_modify_pkg"),
                render: (h, params) => {
                  if (params.row.attachedSku) return "";

                  if (this.isSpuPackage(params.row)) {
                    return (
                      <div>
                        <el-button
                          type="text"
                          size="small"
                          onClick={() => this.handelEditPackage(params.row)}
                        >
                          {this.$t("global_button_edit")}
                        </el-button>
                      </div>
                    );
                  }

                  const statusList = this.getPkgStatusList(params.row);
                  const isWarmingUp = this.isWarmingUpPkg(params.row);
                  // const isSuspended = this.isSuspendedPkg(params.row);
                  const actIsSuspended =
                    this.activity_summary.publish_status === 5;

                  // -1 // 无效状态
                  // 0  // 未发布
                  // 1  // 已发布
                  // 2  // 暂停销售
                  // 3  // 暂停销售 (预热中)
                  let package_status = params.row.package_status;

                  let unpublishBtnHtml = "";
                  if (statusList.includes(0) || isWarmingUp) {
                    unpublishBtnHtml = (
                      <div>
                        <el-button
                          type="text"
                          size="small"
                          disabled={isWarmingUp}
                          onClick={() =>
                            this.handleRoleOperator(params.row, false)
                          }
                        >
                          {this.$t("global_unpublish_1")}{" "}
                          {isWarmingUp ? (
                            <i
                              v-tooltip={{
                                content: this.$t("79647"),
                                visible: true,
                                placement: "bottomRight"
                              }}
                              class="custom-tooltip el-icon-information"
                            />
                          ) : (
                            ""
                          )}
                        </el-button>
                      </div>
                    );
                  }

                  let otherBtnHtml = "";
                  if (package_status !== 1) {
                    // 不是已发布状态
                    otherBtnHtml = (
                      <div>
                        <el-button
                          type="text"
                          size="small"
                          style={{
                            color: "#FF4949",
                            display: "block"
                          }}
                          onClick={() => this.handlerDel(params.row)}
                        >
                          {this.$t("global_delete")}{" "}
                        </el-button>
                        {statusList.includes(1) ||
                        isWarmingUp ||
                        actIsSuspended ? (
                          <span>
                            <el-button
                              type="text"
                              size="small"
                              disabled={isWarmingUp || actIsSuspended}
                              onClick={() =>
                                this.handleRoleOperator(params.row, true)
                              }
                            >
                              {this.$t("global_publish_1")}{" "}
                              {isWarmingUp || actIsSuspended ? (
                                <i
                                  v-tooltip={{
                                    content: isWarmingUp
                                      ? this.$t("79647")
                                      : this.$t("80127"),
                                    visible: true,
                                    placement: "bottomRight"
                                  }}
                                  class="custom-tooltip el-icon-information"
                                />
                              ) : (
                                ""
                              )}
                            </el-button>
                            <br />
                          </span>
                        ) : (
                          ""
                        )}
                        {// 暂停销售没有 archive
                        ![2, 3].includes(package_status) ? (
                          <el-button
                            type="text"
                            size="small"
                            style={{
                              color: "#FF4949"
                            }}
                            onClick={() => this.handlerArchive(params.row)}
                          >
                            {this.$t("package_list_archive")}{" "}
                          </el-button>
                        ) : (
                          ""
                        )}
                      </div>
                    );
                  }

                  let suspendBtnHtml = "";
                  if (statusList.includes(2)) {
                    suspendBtnHtml = (
                      <el-button
                        type="text"
                        size="small"
                        class="status--suspended"
                        onClick={() => this.handleSuspend(params.row)}
                      >
                        {this.$t("79630")}
                        <i
                          v-tooltip={{
                            content: this.$t("79634"),
                            visible: true,
                            placement: "bottomRight"
                          }}
                          class="custom-tooltip el-icon-information"
                        />
                      </el-button>
                    );
                  }

                  return (
                    <div>
                      <el-button
                        type="text"
                        size="small"
                        onClick={() => this.handelEditPackage(params.row)}
                      >
                        {this.$t("global_button_edit")}
                      </el-button>
                      {unpublishBtnHtml}
                      {otherBtnHtml}
                      {suspendBtnHtml}
                    </div>
                  );
                }
              },
              // ...(window.KLK_PAGE_DATA._roles.publish_package_by_lang
              //   ? [
              //       {
              //         minWidth: "160px",
              //         label: __("manage_language_status"),
              //         render: (h, params) => {
              //           if (params.row.main_package_id === 0) {
              //             return (
              //               <el-button
              //                 type="text"
              //                 size="small"
              //                 onClick={() =>
              //                   this.handlePackageListManage(params.row)
              //                 }
              //               >
              //                 {" "}
              //                 {this.$t("package_list_manage")}{" "}
              //               </el-button>
              //             );
              //           }
              //         }
              //       }
              //     ]
              //   : []),
              {
                label: __("package_unit"),
                prop: "currentUnit.unit_name"
              },
              {
                label: __("pm_price_model"),
                render: (h, params) => {
                  if (params.row.package_pricing_model === 1) {
                    return <span>{this.$t("pm_price_calendar")}</span>;
                  } else {
                    return <span>{this.$t("pm_fixed_price")}</span>;
                  }
                }
              },
              {
                minWidth: "150px",
                label: __("package_price_cost"),
                render: (h, params) => {
                  if (params.row.package_pricing_model === 1) {
                    let custom = this.getPriceContentBySkuID(params.row);
                    if (custom.cost_price_range) {
                      let cost = custom.cost_price_range;
                      return (
                        <p
                          style={{
                            "word-break": "break-word",
                            color:
                              cost.currency !==
                              this.activity_summary.merchant_currency
                                ? "red"
                                : "inherit"
                          }}
                        >{`${cost.currency}
                         ${parseDotPrice(
                           cost.min_price,
                           cost.currency
                         )}-${parseDotPrice(
                          cost.max_price,
                          cost.currency
                        )}`}</p>
                      );
                    }
                    return <span>-</span>;
                  }
                  let { cost, cost_currency } = params.row.currentUnit;

                  return (
                    <p
                      style={{
                        "word-break": "break-word",
                        color:
                          cost_currency !==
                          this.activity_summary.merchant_currency
                            ? "red"
                            : "inherit"
                      }}
                    >
                      {cost_currency} {parseDotPrice(cost, cost_currency)}
                    </p>
                  );
                }
              },
              ...(this.isTour
                ? []
                : [
                    {
                      minWidth: "150px",
                      label: __("package_price_retail"),
                      render: (h, params) => {
                        if (params.row.package_pricing_model === 1) {
                          let custom = this.getPriceContentBySkuID(params.row);
                          if (custom.market_price_range) {
                            let retail = custom.market_price_range;
                            return (
                              <p>{`${retail.currency} ${parseDotPrice(
                                retail.min_price,
                                retail.currency
                              )}-${parseDotPrice(
                                retail.max_price,
                                retail.currency
                              )}`}</p>
                            );
                          }
                          return <span>-</span>;
                        }
                        return (
                          <p style="word-break: break-word;">
                            {params.row.sell_currency}{" "}
                            {parseDotPrice(
                              params.row.currentUnit.retail,
                              params.row.currency
                            )}
                          </p>
                        );
                      }
                    }
                  ]),
              {
                minWidth: "150px",
                label: __("package_price_selling"),
                render: (h, params) => {
                  if (params.row.package_pricing_model === 1) {
                    let custom = this.getPriceContentBySkuID(params.row);
                    if (custom.selling_price_range) {
                      let selling = custom.selling_price_range;
                      return (
                        <p>{`${selling.currency} ${parseDotPrice(
                          selling.min_price,
                          selling.currency
                        )}-${parseDotPrice(
                          selling.max_price,
                          selling.currency
                        )}`}</p>
                      );
                    }
                    return <span>-</span>;
                  }
                  return (
                    <p style="word-break: break-word;">
                      {params.row.sell_currency}{" "}
                      {parseDotPrice(
                        params.row.currentUnit.selling,
                        params.row.sell_currency
                      )}
                    </p>
                  );
                }
              },
              {
                minWidth: "170px",
                label: __("package_special_selling_price"),
                render: (h, params) => {
                  if (params.row.package_pricing_model === 1) {
                    let custom = this.getPriceContentBySkuID(params.row);
                    let list = custom.special_selling_price_range;
                    if (Array.isArray(list) && list.length) {
                      return (
                        <div style="word-break: break-word;">
                          {list.map(item => {
                            return (
                              <p>{`${item.currency} ${parseDotPrice(
                                item.min_price,
                                item.currency
                              )} - ${parseDotPrice(
                                item.max_price,
                                item.currency
                              )}`}</p>
                            );
                          })}
                        </div>
                      );
                    }
                    return "";
                  }
                  let { custom } = params.row.currentUnit;
                  if (Array.isArray(custom) && custom.length) {
                    return (
                      <div style="word-break: break-word;">
                        {custom.map(item => {
                          if (item.type === 0)
                            return (
                              <p>{`${item.currency} ${parseDotPrice(
                                item.amount,
                                item.currency
                              )}`}</p>
                            );
                          return "";
                        })}
                      </div>
                    );
                  }
                  return "";
                }
              },
              // {
              //   minWidth: "150px",
              //   label: __("same_day_booking_price"),
              //   render: (h, params) => {
              //     let { custom } = params.row.currentUnit;
              //     if (Array.isArray(custom) && custom.length) {
              //       return (
              //         <div>
              //           {custom.map(item => {
              //             if (item.type === 1)
              //               return <p>{`${item.currency} ${item.amount}`}</p>;
              //             return "";
              //           })}
              //         </div>
              //       );
              //     }
              //     return "";
              //   }
              // },
              {
                minWidth: "90px",
                label: __("package_discount"),
                render: (h, params) => {
                  let { selling, retail } = params.row.currentUnit;
                  return <p>{showDiscount(selling, retail)}</p>;
                }
              },
              {
                minWidth: "105px",
                label: __("package_list_unit_status"),
                render: (h, params) => {
                  let { published } = params.row.currentUnit;

                  return (
                    <p>
                      {published
                        ? __("package_list_unit_published")
                        : __("package_list_unit_unpublished")}
                      <br />
                      <a
                        class="el-button--text"
                        href={`/${
                          window.KLK_LANG_PATH
                        }act/activity/event_logs/${
                          this.activity_id
                        }?package_id=${params.row.package_id}`}
                      >
                        {this.$t("activity_event_logs")}
                      </a>
                    </p>
                  );
                }
              },
              {
                // fixed: 'right',
                minWidth: "120px",
                label: __("package_list_modify_unit"),
                render: (h, params) => {
                  return (
                    <div style="display: flex; flex-direction: column; align-items: flex-start;">
                      {params.row.currentUnit.showPriceEdit ? (
                        <el-button
                          type="text"
                          size="small"
                          onClick={() => this.handleEditPrice(params.row, "bd")}
                        >
                          {this.$t("package_edit_price")}
                        </el-button>
                      ) : (
                        ""
                      )}
                      {this.isSpuPackage(params.row) ? (
                        ""
                      ) : (
                        <el-button
                          type="text"
                          size="small"
                          style={{
                            color: params.row.currentUnit.published
                              ? "#FF4949"
                              : "#1079Fb"
                          }}
                          onClick={() => this.handleUnitPublish(params.row)}
                        >
                          {params.row.currentUnit.published
                            ? this.$t("package_unit_unpublish")
                            : this.$t("package_unit_publish")}
                        </el-button>
                      )}
                    </div>
                  );
                }
              }
            ]
          : [
              // em package table
              {
                minWidth: "90px",
                label: "Matched Specs",
                prop: "tips"
              },
              {
                minWidth: "175px",
                label: __("act_list_specs_status"),
                render: (h, params) => {
                  let color = ["#ff5722", "#02ac98", "#ffa628"][
                    params.row.spec_status
                  ];
                  let status = [
                    "act_em_specs_status_no",
                    "act_em_specs_status_matched",
                    "act_em_specs_status_pending"
                  ][params.row.spec_status];

                  return (
                    <span
                      style={{
                        color: color
                      }}
                    >
                      {this.$t(status)}
                    </span>
                  );
                }
              },
              {
                minWidth: "120px",
                label: __("package_type"),
                render: (h, params) => {
                  // is_add_on
                  if (params.row.attachedSku) return "";

                  return (
                    <p>
                      {!params.row.main_package_id
                        ? this.$t("main_package")
                        : this.$t("merge_package")}
                    </p>
                  );
                }
              },
              {
                label: __("package_unit"),
                prop: "currentUnit.unit_name"
              },
              {
                minWidth: "130px",
                // fixed: 'right',
                label: __("package_list_modify"),
                render: (h, params) => {
                  const isCard = params.row.display_package_card === 1;

                  if (this.isSpuPackage(params.row)) {
                    return "-";
                  }

                  return (
                    <div style="display: flex; flex-direction: column; align-items: flex-start;">
                      {this.canEditSpecs ? (
                        <el-button
                          size="small"
                          type="text"
                          title={
                            isCard ? this.$t("display_package_card_tips") : ""
                          }
                          disabled={isCard}
                          onClick={() => this.handleEditSpecs(params.row)}
                        >
                          Edit Specs
                        </el-button>
                      ) : (
                        ""
                      )}
                    </div>
                  );
                }
              }
            ])
      ];
    },
    filterBdColumns() {
      return this.bdColumns.map((col, index) => {
        return {
          key: index,
          value: col.label
        };
      });
    },
    currentBdColumns() {
      return this.bdColumns.filter((item, index) =>
        this.filterBdData.includes(index)
      );
    }
  },
  methods: {
    handleRouterLink() {
      let scrollTop = document.documentElement.scrollTop;
      setTimeout(() => {
        document.documentElement.scrollTo(0, scrollTop);
        scrollTop = null;
      });
    },
    isSpuPackage(row) {
      return row.can_not_update_status;
    }
  }
};

const batch_package_mixin = {
  data() {
    return {
      batchPkg: {
        visible: false,
        packages: [], // filter the package data before
        items: [], // filter the package data after
        toPublish: false
      }
    };
  },
  computed: {
    publishPkgList() {
      return this.batchPkg.packages.filter(pkg => {
        const statusList = this.getPkgStatusList(pkg);
        const isWarmingUp = this.isWarmingUpPkg(pkg);

        return statusList.includes(1) && !isWarmingUp;
      });
    },
    unpublishPkgList() {
      return this.batchPkg.packages.filter(pkg => {
        const statusList = this.getPkgStatusList(pkg);

        return statusList.includes(0);
      });
    }
  },
  watch: {
    tableData: {
      deep: true,
      handler() {
        let packages = this.package_summary.map(item => {
          let name = _.find(item.package_local, {
            language_type: this.currentBLang
          }).name;

          if (!name) {
            name = _.find(item.package_local, {
              language_type: "en_US"
            }).name;
          }

          return {
            key: item.package_id,
            value: item.package_id + " - " + name,
            package_next_status: item.package_next_status,
            package_status: item.package_status, // control btn
            can_not_update_status: item.can_not_update_status
          };
        });

        this.$set(this.batchPkg, "packages", packages);
      }
    }
  },
  methods: {
    handleBatchPackage({ toPublish = true } = {}) {
      // Filter out SPU packages
      let items = (toPublish
        ? this.publishPkgList
        : this.unpublishPkgList
      ).filter(pkg => !this.isSpuPackage(pkg));

      this.$set(this, "batchPkg", {
        packages: this.batchPkg.packages,
        visible: true,
        items,
        toPublish
      });
    }
  }
};

const batch_unit_mixin = {
  data() {
    return {
      batchUnit: {
        visible: false,
        packages: [],
        toPublish: false,
        // control btn to display
        canPublish: false,
        canUnpublish: false
      }
    };
  },
  watch: {
    tableData: {
      deep: true,
      handler() {
        let packages = Object.values(
          this.tableData.reduce((acc, curr) => {
            if (!acc[curr.package_id]) {
              acc[curr.package_id] = {
                package_id: curr.package_id,
                package_name: curr.currentLocal.name,
                canPublishUnit: [],
                canUnpublishUnit: [],
                currentUnit: [],
                data: [],
                can_not_update_status: curr.can_not_update_status
              };
            }

            let { sku_id, unit_name, published, sku_type } = curr.currentUnit;
            let cell = {
              key: sku_id,
              value: unit_name,
              published,
              sku_type // 0 无共享， 1 子sku, 2 主sku
            };

            if (published) {
              acc[curr.package_id].canUnpublishUnit.push(cell);
              this.batchUnit.canUnpublish = true;
            } else {
              acc[curr.package_id].canPublishUnit.push(cell);
              this.batchUnit.canPublish = true;
            }

            return acc;
          }, {})
        );

        this.$set(this.batchUnit, "packages", packages);
      }
    }
  },
  methods: {
    handleBatchUnit({ toPublish = true } = {}) {
      Object.assign(this.batchUnit, {
        visible: true,
        toPublish,
        packages: this.batchUnit.packages
          .filter(pkg => !this.isSpuPackage(pkg))
          .map(item => ({
            ...item,
            currentUnit: toPublish ? item.canPublishUnit : item.canUnpublishUnit
          }))
      });
    }
  }
};

const search_mixins = {
  data() {
    return {
      search: {
        status: -1
      },
      statusOpts: [
        {
          label: __("all"),
          value: -1
        },
        {
          label: __("global_unpublished"),
          value: 0 // package_status: 0
        },
        {
          label: __("global_published"),
          value: 1 // package_status: 1
        }
      ],
      currentTableData: []
    };
  },
  watch: {
    tableData: {
      deep: true,
      immediate: true,
      handler() {
        this.getCurrentTableData();
      }
    },
    search: {
      deep: true,
      immediate: true,
      handler() {
        this.getCurrentTableData();
      }
    }
  },
  methods: {
    getCurrentTableData: _.debounce(function() {
      if (-1 === this.search.status) {
        this.$set(this, "currentTableData", this.tableData);
      } else {
        this.$set(
          this,
          "currentTableData",
          this.tableData.filter(
            item => item.package_status === this.search.status
          )
        );
      }
    }, 100)
  }
};

const suspend_mixin = {
  components: {
    SuspendDialog
  },
  data() {
    return {
      suspend: {
        visible: false,
        type: "pkg",
        title: __("79631"),
        id: -1,
        handleSuspendSubmit: () => {
          this.initData();
        }
      }
    };
  },
  methods: {
    getPkgStatusList(row) {
      //PackageStatusInvalid             int64 = -1 // 无效状态
      // PackageStatusUnpublished         int64 = 0  // 未发布
      // PackageStatusPublished           int64 = 1  // 已发布
      // PackageStatusSuspended           int64 = 2  // 暂停销售
      // PackageStatusTocSuspendWarmingUp int64 = 3  // 暂停销售 (预热中)
      return row.package_next_status?.status_list ?? [];
    },
    isSuspendedPkg(row) {
      return [2, 3].includes(row.package_status);
    },
    isWarmingUpPkg(row) {
      return row.package_status === 3;
    },
    handleSuspend(row) {
      Object.assign(this.suspend, {
        visible: true,
        id: row.package_id
      });
    }
  }
};

export default {
  name: "PackageList",
  components: {
    TplBatchUnitDialog,
    TplBatchPackageDialog,
    TplPkgLangStatusDialog,
    TplManageLanguage,
    ListTable,
    CommonCb
  },
  mixins: [
    gds_conf_mixin,
    bd_conf_mixin,
    batch_package_mixin,
    batch_unit_mixin,
    search_mixins,
    suspend_mixin
  ],
  data() {
    return {
      modifiableHintVisible: false, // ?? Old codes directly display: none; no more associated codes.

      act_name: "",
      template_id: "",
      actPosition: {},
      tableData: [],
      priceFromCalender: [],
      activity_summary: {},
      package_summary: [],
      UNIT_PUBLISHED_NUM: {},

      manageLanguageProps: {
        activity_id: undefined,
        template_id: undefined,
        is_edit: true,
        url: "",
        visible: false
      },

      oldtableDate: [],

      pkgLangStatusDialog: {
        visible: false,
        id: undefined
      },
      sub_category_id: ""
    };
  },
  computed: {
    activity_id() {
      return +this.$route.params.activity_id;
    },
    activityName() {
      return _.get(this.activity_summary, "currentLocal.title", "");
    },
    canEditSpecs() {
      return (
        window.KLK_PAGE_DATA._roles.canEnterCategoryAttributes &&
        !admin_const.spu_template_ids.includes(this.template_id)
      );
    },
    canAddNew() {
      return !(
        !this.actPosition.main_package ||
        this.actPosition.add_on_package === 1 ||
        admin_const.spu_template_ids.includes(this.template_id)
      );
    },
    currentBLang() {
      return lang_conf.getLangObj("F_LANG", "B_LANG")[window.KLK_LANG];
    },
    hasAmPermission() {
      return klook.userHasPermission(
        "act/activity_list_page/package_list_page/am_table"
      );
    },
    hasEmPermission() {
      return klook.userHasPermission(
        "act/activity_list_page/package_list_page/em_table"
      );
    },
    isActAdmin() {
      return klook.userHasPermission("act/activity_list_page/show_all_act");
    },
    isAmPage() {
      return this.hasAmPermission && this.$route.query.role !== "content";
    },
    canSwitchList() {
      return this.isActAdmin || (this.hasAmPermission && this.hasEmPermission);
    },
    hasGdsPermission() {
      return klook.userHasPermission(
        "act/activity_list_page/package_list_page/gds_table"
      );
    },
    hasGlobalGdsPermission() {
      return klook.userHasPermission(
        "act/activity_list_page/package_list_page/global_gds_price_edit"
      );
    },
    hasChinaAgentPermission() {
      return klook.userHasPermission(
        "act/activity_list_page/package_list_page/china_agent_price_edit"
      );
    },
    isTour() {
      // const sub_category_id = this.sub_category_id;
      // const ids = [2];
      // return ids.includes(sub_category_id);
      return false;
    }
  },
  //  created() {
  //     this.getPriceFromCalender();
  // },
  beforeMount() {
    this.initData();
  },
  methods: {
    toSetting() {
      const id = this.$route.params.activity_id || "";
      const url = `/mspa/experiencesadmincommon/act/pkg_publish_limit?id=${id}`;
      window.open(url, "_blank");
    },
    handleAddNew() {
      window.location.href = main.setQuery(
        {
          package_type: 1,
          activity_id: this.activity_id,
          package_id: "null",
          lang: "en_US"
        },
        main.setPathname(`/act/package/info`)
      );
    },
    async initData() {
      const res = await ADMIN_API.getActLang({
        activity_id: this.activity_id
      });
      this.template_id = res.result.template_id;
      this.sub_category_id = res.result.sub_category_id;

      this.actPosition = (await klook.pmsGet(
        klook.parseAjaxUrl(
          "/prosrv/activities/" + this.activity_id + "/position"
        )
      )).result;

      await this.getPriceFromCalender();
      await this.getPrivsData();
    },
    async getPriceFromCalender() {
      let resp = await ADMIN_API.getPriceCalenderByActID({
        activity_id: this.activity_id
      });
      this.$set(this, "priceFromCalender", resp.result.package_prices_range);
    },
    getPriceContentBySkuID(row) {
      if (!this.priceFromCalender.length) {
        return {};
      }
      let target = this.priceFromCalender.filter(item => {
        return item.package_id === row.package_id;
      })[0];
      let currSku = target.sku_prices_range.filter(item => {
        return item !== null && item.sku_id === row.currentUnit.sku_id;
      })[0];
      if (currSku === undefined) {
        return {};
      }
      return currSku;
    },
    async getPrivsData() {
      let resp = await ADMIN_API.getActPkgPrice({
        activity_id: this.activity_id,
        params: {
          page_type: "ACT,SPU,ATT",
          custom_language: (await ADMIN_API.getActLang({
            activity_id: this.activity_id
          })).result.language //  use published language
        }
      });

      // sort unit from published to unpublished
      if (_.isEmpty(resp.result.package_summary)) {
        window.alert(
          "A package becomes editable in package list only after its schedule has been initialized. For now, we can't find any package editable here :("
        );
        history.back();
        klook.abort();
      }

      // await this.getPriceFromCalender();

      this.$set(this, "activity_summary", {
        ...resp.result.activity_summary,
        currentLocal:
          _.find(resp.result.activity_summary.local, {
            language: this.currentBLang
          }) || {}
      });

      resp.result.package_summary = resp.result.package_summary.map(pkg => ({
        ...pkg,
        price: _.orderBy(pkg.price, "published", "desc")
      }));

      let tableData = resp.result.package_summary
        // sort package sequence with published first
        .filter(pkg => pkg.package_status === 1)
        .concat(
          resp.result.package_summary.filter(pkg => pkg.package_status !== 1)
        );

      this.package_summary = _.cloneDeep(
        tableData.filter(pkg =>
          pkg.price.some(sku => (sku.price_local || []).length)
        )
      );

      tableData.forEach(pkg => {
        pkg.price.forEach(unit => {
          if (unit.price_value.published) {
            if (typeof this.UNIT_PUBLISHED_NUM[pkg.package_id] === "number") {
              this.UNIT_PUBLISHED_NUM[pkg.package_id] += 1;
            } else {
              this.UNIT_PUBLISHED_NUM[pkg.package_id] = 1;
            }
          }
        });
      });

      this.$set(
        this,
        "tableData",
        tableData.reduce((acc, curr) => {
          // new wash data
          let showPriceEdit = curr.product_type === 1 ? false : true;
          return [
            ...acc,
            ...curr.price
              .filter(sku => (sku.price_local || []).length) // why hide the package which is no sku
              .map((sku, index) => {
                return {
                  ...curr,
                  attachedSku: index !== 0,
                  currentLocal: _.find(curr.package_local, {
                    language_type: this.currentBLang
                  }),
                  currentUnit: {
                    ...sku.price_value,
                    ...(_.find(sku.price_local, {
                      language_type: this.currentBLang
                    }) || {}),
                    showPriceEdit
                  },
                  // for jsx
                  visiblePriority: false
                };
              })
          ];
        }, [])
      );
    },
    handleBackActivityList() {
      window.location.href = `/${window.KLK_LANG_PATH}act/activity/list`;
    },
    handleGoToArchivePackageList() {
      this.$router.push({
        name: "package_list_archive",
        query: this.$route.query
      });
    },
    handleEditPrice(data, role = "gds") {
      let search = new URLSearchParams(location.search);
      search.set("sku_id", data.currentUnit.sku_id);
      let href = `/${KLK_LANG_PATH}act/activity/${this.activity_id}/price/${
        data.package_id
      }/modify_${role}?${search}`;
      if (role === "gds") {
        search.set("role", role);
        href = `/${KLK_LANG_PATH}act/activity/${this.activity_id}/price/${
          data.package_id
        }/agent_modify?${search}`;
      }
      window.location.href = href;
    },
    handleEditSpecs() {
      window.location.href = `/${KLK_LANG_PATH}act/${
        this.activity_id
      }/specs/list`;
    },
    async handelEditPackage(row) {
      if (this.hasAmPermission) {
        try {
          await editlock.checkLock(this.activity_id, false, null, {
            user_type: 0,
            language: "ALL"
          });
        } catch (error) {
          // 如果活动已迁移，checkLock 会抛出 ACTIVITY_MIGRATED 异常并重定向
          if (error.message === 'ACTIVITY_MIGRATED') {
            return;
          }
          throw error;
        }
      }

      let url = `/${window.KLK_LANG_PATH}act/package/info?activity_id=${
        this.activity_id
      }&package_id=${row.package_id}&package_type=1&lang=en_US`;

      if (this.isSpuPackage(row)) {
        url = `/mspa/experiencesadmincommon/aid/tours/basic/${
          this.activity_id
        }?lang=en_US&package_id=${row.package_id}&package_type=1&is_am=${
          this.isAmPage
        }`;
      }

      this.$set(this, "manageLanguageProps", {
        activity_id: this.activity_id,
        template_id: this.template_id,
        url,
        visible: true
      });
    },
    // @type: Boolean, true: to publish
    async handleRoleOperator(row, type) {
      let popupStr = klook.strformat(
        __("confirm_publish_pkg"),
        type ? __("global_publish_1") : __("global_unpublish_1"),
        row.package_id
      );
      // generate specific popup string base on status of auto_publish
      let auto_published_str = "";
      let time_str = "";
      let publish_str = "";
      if (type && row.auto_publish.published) {
        auto_published_str = "auto-published";
        time_str = row.auto_publish.published_time;
        publish_str = "publish";
        popupStr = `The package has set to be ${auto_published_str} by ${time_str}, are you sure to ${publish_str} it now?`;
      }
      if (!type && row.auto_publish.unpublished) {
        auto_published_str = "auto-unpublished";
        time_str = row.auto_publish.unpublished_time;
        publish_str = "unpublish";
        popupStr = `The package has set to be ${auto_published_str} by ${time_str}, are you sure to ${publish_str} it now?`;
      }

      await this.$confirm(popupStr);
      if (!type) {
        // published to unpublish
        await main.checkActivityInfo(row.package_id, "package");
      }

      let checkPassRes = {};
      if (!type) {
        // 下架活动、套餐、sku的时候需要检查是否是 pass standard
        checkPassRes = await checkPassAsync({ package_ids: row.package_id });

        if (checkPassRes && checkPassRes.stop) {
          return;
        }
      }

      let resp = await ADMIN_API.pkgPublished({
        data: {
          package_id: row.package_id,
          published: type,
          reason: checkPassRes.reason,
          reason_code:
            checkPassRes.reasonCode === undefined
              ? undefined
              : String(checkPassRes.reasonCode),
          force: !!checkPassRes.force
        },
        just_throw: false
      });
      if (resp.success) {
        this.$message(this.$t("global_success"));
      } else {
        await this.$alert(resp.error.message);
      }
      await this.getPrivsData();
    },
    async updatePackagePreviewTag(row) {
      await ADMIN_API.updatePkgPreview({
        data: {
          package_id: row.package_id,
          preview_tag: row.package_preview_tag
        }
      });
      await this.getPrivsData();
    },
    async handlerDel(row) {
      await this.$confirm(
        `${this.$t("package_list_delete_package_tips")} id: ${row.package_id}`
      );
      await ADMIN_API.pkgDestroy({
        package_id: row.package_id
      });
      this.$message(this.$t("global_success"));
      await this.getPrivsData();
    },
    async handlerArchive(row) {
      await this.$confirm(
        `${this.$t("js_confirm_archive")} id: ${row.package_id}`
      );
      await ADMIN_API.pkgArchive({
        package_id: row.package_id
      });
      this.$message(this.$t("global_success"));
      await this.getPrivsData();
    },
    async handlerUnArchive(row) {
      await this.$confirm(
        `${this.$t("js_confirm_unarchive")} id: ${row.package_id}`
      );
      await ADMIN_API.pkgUnArchive({
        package_id: row.package_id
      });
      this.$message(this.$t("global_success"));
      await this.getPrivsData();
    },
    validatorPriority(rule, value, callback) {
      if (value && !value.trim()) {
        callback(new Error(this.$t("global_validate_text_required")));
      } else if (!/^\d+$/.test(value)) {
        callback(new Error(this.$t("global_validate_number_required")));
      } else {
        callback();
      }
    },
    async handleUpdatePriority(row) {
      await ADMIN_API.updatePackageSort(
        this.tableData.reduce((acc, curr) => {
          if (curr.attachedSku) return acc;

          return [
            ...acc,
            {
              package_id: curr.package_id,
              priority:
                row.package_id === curr.package_id
                  ? +row.tempPriority
                  : curr.priority
            }
          ];
        }, [])
      );

      this.$message(this.$t("global_success"));
      await this.getPrivsData();
    },
    handlePackageListManage(row) {
      this.$set(this, "pkgLangStatusDialog", {
        visible: true,
        id: row.package_id
      });
    },
    handleUnitPublish(row) {
      let { sku_id, published } = row.currentUnit;
      let doPublish = !published;

      if (doPublish) {
        // Do publish
        // FND类目把unit上限改为100
        let max = this.template_id === 4 ? 100 : 8;
        if (this.UNIT_PUBLISHED_NUM[row.package_id] >= max) {
          this.$alert(this.$t("package_price_units_overflow"));
          return false;
        }
      }

      let _resultPromise = doPublish
        ? Promise.resolve()
        : main.checkActivityInfo(sku_id, "sku");

      _resultPromise
        .then(async () => {
          let checkPassRes = {};
          if (!doPublish) {
            // 下架活动、套餐、sku的时候需要检查是否是 pass standard
            checkPassRes = await checkPassAsync({ sku_ids: sku_id });

            if (checkPassRes && checkPassRes.stop) {
              return;
            }
          }
          let resp = await ADMIN_API.pkgSkuPublish({
            data: {
              sku_id: sku_id,
              published: doPublish,
              reason: checkPassRes.reason,
              reason_code: checkPassRes.reasonCode,
              force: !!checkPassRes.force
            },
            just_throw: false
          });

          if (resp.success) {
            this.$message(this.$t("global_success"));
            await this.getPrivsData();
          } else {
            this.$alert(
              (resp.error && resp.error.message) || this.$t("global_error")
            );
          }
        })
        .catch(err => {
          this.$alert(err);
        });
    },
    initSortable() {
      this.$nextTick(() => {
        var el = document.querySelector(
          ".package-list-bd .el-table__body-wrapper > table > tbody"
        );

        this.sortable = Sortable.default.create(el, {
          // draggable: '.drag-to-order',
          filter: ".drag-disable-item",
          handle: ".drag-to-order",
          preventOnFilter: true,
          onStart: function(evt) {
            // evt.target.
            this.oldtableDate = _.cloneDeep(this.tableData);
          },
          onEnd: async evt => {
            // Use cloneDeep to prevent pollution the data and use getTableData to update table DOM
            let tableData = _.cloneDeep(this.tableData);
            let currRow = tableData.splice(evt.oldIndex, 1)[0];

            tableData.splice(evt.newIndex, 0, currRow);
            let dataList = [currRow];
            for (let i = evt.newIndex; i >= 0; i--) {
              if (i === 0) {
                break;
              } else {
                if (
                  tableData[i].package_id === currRow.package_id ||
                  tableData[i].attachedSku === true
                ) {
                  continue;
                }
                dataList.unshift(tableData[i]);
              }
            }
            for (let i = evt.newIndex; i < tableData.length; i++) {
              if (
                tableData[i].package_id === currRow.package_id ||
                tableData[i].attachedSku === true
              ) {
                continue;
              }
              dataList.push(tableData[i]);
            }

            await ADMIN_API.updatePackageSortBatch({
              data: dataList.map((item, index) => {
                return {
                  package_id: item.package_id,
                  priority: index
                };
              })
            });

            this.$nextTick(async () => {
              await this.initData();
              this.dragedTableData++;
              this.initSortable();
            });
          }
        });
      });
    }
  },
  mounted() {
    this.initSortable();
  }
};
</script>

<style lang="scss">
$stickyTop: calc(123px + var(--offsetY));

.package-list-container {
  .pkg-list-tip {
    margin-top: 10px;
    &.el-alert--warning {
      background-color: #fdf6ec;
      color: #e6a23c;
      .el-alert__icon {
        color: #e6a23c;
      }
    }
  }

  .header--operation {
    position: sticky;
    top: 0;
    z-index: 99;
    background-color: #fff;
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
  }
  .el-table {
    font-size: 12px;
    .el-button + .el-button {
      margin-left: 0;
    }
  }
  .el-table th > .cell {
    line-height: 2;
    word-break: break-word;
  }
  .el-table__fixed {
    z-index: 10;
    overflow: inherit;
    .el-table__fixed-header-wrapper {
      position: sticky;
      top: $stickyTop;
      z-index: 11;
    }
    .el-table__fixed-header-wrapper,
    .el-table__fixed-body-wrapper {
      width: 100%;
      overflow: hidden;
    }
  }
  .el-table p {
    margin: 0;
  }
  .packages__table {
    position: relative;
    &,
    .el-table {
      overflow: inherit;
    }
    .el-table__header-wrapper {
      position: sticky;
      top: $stickyTop;
      z-index: 9;
    }
  }
  /*.drag_img {*/
  /*    width: 16px;*/
  /*    height: 16px;*/
  /*}*/
  .status--suspended {
    width: fit-content;
    color: #ff4d4f;
    cursor: pointer;
  }

  .custom-tooltip {
    display: inline-block;
    margin-left: 6px;
    font-size: 9px;
    color: rgba(0, 0, 0, 0.85);
    text-align: center;
  }
}
.drag-to-order {
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: move;
  img {
    position: center;
    width: 16px;
    height: 16px;
  }
}
</style>

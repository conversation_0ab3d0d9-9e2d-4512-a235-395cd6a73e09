<template>
  <div class="manage-lang" :class="{ 'is-edit': !is_edit }">
    <!-- 编辑语言 -->
    <el-dialog
      :title="
        is_edit
          ? $t('manage_language_manage_language')
          : $t('manage_language_status')
      "
      :visible.sync="dialogTableVisible"
      @close="refresh"
    >
      <p class="css-note" style="font-size: smaller">
        <span v-html="$t('multi_en_hint')" />
      </p>
      <el-table :data="tableData">
        <!-- 语言 -->
        <el-table-column key="language" :label="$t('global_language')">
          <template scope="scope">
            {{ fmt(scope.row.language) }}
          </template>
        </el-table-column>
        <!-- 语言翻译状态 !is_edit-->
        <el-table-column
          v-if="!is_edit"
          key="translation_status"
          :label="$t('act_list_translation_status')"
        >
          <template scope="scope">
            {{ getTranslationStatusLabel(scope.row) }}
          </template>
        </el-table-column>
        <!-- 语言发布状态 !is_edit-->
        <el-table-column
          key="publish_status"
          :label="$t('act_list_language_publish_status')"
        >
          <template scope="scope">
            {{ getPublishStatusLabel(scope.row) }}
            <br />
            <!-- 按英语发布, 语言状态为草稿, 按部分英语发布, 语言状态为5, -->
            <span
              v-if="scope.row.status === 1 || scope.row.status === 5"
              style="color: gray"
            >
              {{ $t("manage_language_modify_detail") }}
              {{ scope.row.author }}
              {{ scope.row.update_time }}
            </span>
            <el-button
              v-if="scope.row.status"
              type="text"
              size="mini"
              @click="reviewEventLogs(scope.row)"
            >
              {{ $t("activity_event_logs") }}
            </el-button>
          </template>
        </el-table-column>
        <!-- 活动修改 -->
        <el-table-column key="modify" :label="$t('package_list_modify')">
          <template scope="scope">
            <template v-if="is_edit">
              <template v-if="!edit_lang_selected">
                <!-- 新增 -->
                <el-tooltip
                  :disabled="!is_in_block_list(scope.row)"
                  placement="right"
                  :content="$t('act_list_is_on_blocklist')"
                >
                  <el-button
                    v-if="scope.row.status == 0"
                    size="small"
                    type="text"
                    :disabled="noEditLang(scope.row)"
                    @click="add(scope.$index, scope.row)"
                  >
                    {{ $t("global_button_add") }}
                  </el-button>
                  <!-- 编辑 -->
                  <el-button
                    v-else
                    size="small"
                    type="text"
                    :disabled="noEditLang(scope.row)"
                    @click="edit(scope.$index, scope.row)"
                  >
                    {{ $t("global_button_edit") }}
                  </el-button>
                </el-tooltip>
                <el-tooltip
                  v-if="noEditLang(scope.row)"
                  class="item"
                  effect="dark"
                  :content="$t('no_edit_en')"
                  placement="top-start"
                >
                  <i class="el-icon-warning" />
                </el-tooltip>
              </template>
              <el-button
                v-if="edit_lang == scope.row.language"
                size="small"
                type="text"
                @click="edit_lang = ''"
              >
                {{ $t("global_button_cancel") }}
              </el-button>
            </template>
            <template v-else>
              <!-- TEMP hide this copy function for no op power -->
              <!-- <el-button v-if="!is_wifi && copyReady(scope.row)" size="small" type="text" @click="copyEnglish(scope.row)">{{$t('copy_multi_english')}}</el-button> -->
              <!--draft => Submit to Preview-->
              <!-- 发布按钮 -->
              <el-tooltip
                v-if="scope.row.status == 2"
                :disabled="!is_in_block_list(scope.row)"
                placement="right"
                :content="$t('act_list_is_on_blocklist')"
              >
                <span>
                  <el-button
                    :disabled="is_in_block_list(scope.row)"
                    size="small"
                    type="text"
                    @click="
                      change_publish(scope.$index, scope.row, true, false)
                    "
                  >
                    {{ $t("global_publish") }}
                  </el-button>
                </span>
              </el-tooltip>
              <!-- 取消发布按钮 -->
              <el-button
                v-else-if="scope.row.status == 3"
                size="small"
                type="text"
                @click="change_publish(scope.$index, scope.row, false, false)"
              >
                {{ $t("global_unpublish_1") }}
              </el-button>
              <!-- 发布按钮 -->
              <el-tooltip
                v-else-if="scope.row.status == 4"
                :disabled="!is_in_block_list(scope.row)"
                placement="right"
                :content="$t('act_list_is_on_blocklist')"
              >
                <span>
                  <el-button
                    size="small"
                    type="text"
                    :disabled="is_in_block_list(scope.row)"
                    @click="
                      change_publish(scope.$index, scope.row, true, false)
                    "
                  >
                    {{ $t("global_publish") }}
                  </el-button>
                </span>
              </el-tooltip>
              <!-- 预览按钮 -->
              <el-button
                v-else-if="
                  act_status !== 2 &&
                    scope.row.status === 1 &&
                    !am_page &&
                    !scope.row.default_show_english
                "
                size="small"
                type="text"
                @click="jumpToSubmit(scope)"
              >
                {{ $t("act_submit_to_preview") }}
              </el-button>
              <span v-else> - </span>
            </template>
          </template>
        </el-table-column>
        <!-- 英文内容 -->
        <el-table-column
          v-if="!is_edit"
          key="pub_english_content"
          :label="$t('act_english_content')"
        >
          <template scope="scope">
            <template v-if="can_pub_en(scope.row)">
              <!-- 取消发布英文按钮 -->
              <el-button
                v-if="scope.row.default_show_english"
                size="small"
                type="text"
                @click="change_publish(scope.$index, scope.row, false, true)"
              >
                {{ $t("act_list_cancel_en_US_publish") }}
              </el-button>
              <!-- 发布为美国英文按钮 -->
              <el-tooltip
                v-else
                :disabled="!is_in_block_list(scope.row)"
                placement="right"
                :content="$t('act_list_is_on_blocklist')"
              >
                <span>
                  <el-button
                    size="small"
                    type="text"
                    :disabled="
                      is_in_block_list(scope.row) || scope.row.status === 5
                    "
                    @click="change_publish(scope.$index, scope.row, true, true)"
                  >
                    {{ $t("act_list_publish_with_en_US") }}
                  </el-button>
                </span>
              </el-tooltip>
            </template>
            <template v-else>
              -
            </template>
          </template>
        </el-table-column>
        <!-- 管理语言状态 => publish with ai -->
        <el-table-column
          v-if="!is_edit && show_publish_with_ai_column"
          key="publish_with_ai"
          :label="$t('act_list_publish_with_AI_translation')"
          :render-header="render_publish_with_ai_header"
        >
          <template scope="scope">
            <el-button
              v-if="scope.row.status === 5"
              size="small"
              type="text"
              @click="
                change_publish(scope.$index, scope.row, false, false, true)
              "
            >
              {{ $t("act_list_cancel_AI_translation_publish") }}
            </el-button>
            <!-- 是否展示 publish with ai btn -->
            <!-- 已经按美国英文发布了, 不能点击按机翻发布 -->
            <el-tooltip
              v-else-if="show_pub_ai_btn(scope.row)"
              :disabled="!is_in_block_list(scope.row)"
              placement="right"
              :content="$t('act_list_is_on_blocklist')"
            >
              <span>
                <el-button
                  size="small"
                  type="text"
                  :disabled="
                    is_in_block_list(scope.row) ||
                      !can_operate_pub_ai_btn(scope.row)
                  "
                  @click="
                    change_publish(scope.$index, scope.row, true, false, true)
                  "
                >
                  {{ $t("act_list_publish_with_AI_translation") }}
                </el-button>
              </span>
            </el-tooltip>
            <span v-else>-</span>
          </template>
        </el-table-column>
      </el-table>
      <template slot="footer">
        <div v-if="edit_lang_selected" style="text-align: left;">
          {{ $t("manage_language_reference_lang") }}
          <el-radio-group v-model="ref_lang" class="ref">
            <el-radio
              v-for="item in ref_lang_options"
              :key="item.language"
              :label="item.language"
            >
              {{ fmt(item.language) }}
            </el-radio>
          </el-radio-group>
          <span v-if="ref_lang_options.length == 0">{{
            $t("ref_lang_none")
          }}</span>
        </div>
        <span v-if="act_status >= 0" style="float: left; margin-top: 10px;">{{
          $t("global_activity_status") + ": " + act_status_map[act_status]
        }}</span>
        <el-button style="float:right;margin:0 10px" @click="cancel">
          {{ $t("cancel") }}
        </el-button>
        <!-- FIXME known bug: confirm button may not show up on Safari -->
        <el-button
          v-if="edit_lang_selected"
          style="float:right"
          type="primary"
          @click="confirm"
        >
          {{ $t("confirm") }}
        </el-button>
      </template>
    </el-dialog>

    <el-dialog
      title="Copy English"
      :visible.sync="copyEnglishDialog"
      @close="$emit('confirm-copy', false)"
    >
      <el-form label-width="200px">
        <el-form-item>
          {{ $t("copy_english_hint") }}
        </el-form-item>
        <el-form-item label="Copy From:">
          {{ copy_from }}
        </el-form-item>
        <el-form-item label="Copy To:">
          <el-select v-model="copy_to" multiple filterable>
            <el-option
              v-for="lang in readyEnglishLangs"
              :key="lang"
              :label="lang"
              :value="lang"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="copyEnglishDialog = false">Cancel</el-button>
        <el-button type="primary" @click="confirmCopyEnglish"
          >Confirm</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
const editlock = require("../../../../pluginit/editlock.js");

const statusTable = {
  // this is status for language, nothing like status for activity.
  0: __("package_language_none"),
  1: __("global_draft"),
  2: __("act_list_in_preview"),
  3: __("global_published"),
  4: __("global_unpublished")
};
// 翻译状态
const translation_status_map = {
  0: __("act_list_translation_status_none"),
  1: __("act_list_translation_status_pending"),
  2: __("act_list_translation_status_done")
};
// 获取语言翻译状态
async function getTranslateStatus(activity_id) {
  const res = await ADMIN_API.getActTranslateStatus(activity_id);
  const statuses = _.get(res, "result.statuses.0.items") || [];
  return statuses.reduce((acc, cur) => {
    acc[cur.language] = cur.status;
    return acc;
  }, {});
}

const copy_english_mixin = {
  data() {
    return {
      copyEnglishDialog: false,
      copy_from: "",
      copy_to: ""
    };
  },
  computed: {
    readyEnglishLangs() {
      return this.tableData
        .filter(v => v.status < 2 && v.language.match("en"))
        .map(v => v.language);
    }
  },
  methods: {
    confirmCopyEnglish() {
      this.$emit("confirm-copy", true);
      this.copyEnglishDialog = false;
    }
  }
};

export default {
  name: "TplManageLanguage",
  mixins: [copy_english_mixin],
  model: {
    value: "visible",
    event: "change"
  },
  props: {
    visible: {
      type: Boolean,
      required: true
    },
    activity_id: {
      // id
      type: Number,
      required: true,
      default: 0
    },
    template_id: {
      type: Number,
      required: true,
      default: 0
    },
    act_status: {
      type: Number,
      default: undefined
    },
    is_edit: {
      type: Boolean,
      default: true
    },
    url: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      tableData: [],
      is_am: window.KLK_PAGE_DATA._roles.is_am,
      en_standardization_status: null, // 英文是否已经结构化
      blocked_languages: [], // 屏蔽的语言

      edit_lang: "",
      ref_lang: "",
      add_flag: false,
      act_status_map: {
        0: __("global_unpublished"),
        1: __("global_published"),
        2: __("global_draft"),
        3: __("act_list_content_to_edit"),
        4: __("act_list_in_preview")
      }
    };
  },
  computed: {
    am_page() {
      return window.am_page; // activity list
    },
    has_em_access() {
      return window.KLK_PAGE_DATA._roles.has_em_access;
    },
    // 美英是否结构化完成
    is_en_standardization_finished() {
      // 0-未结构化 1-结构化中 2-结构化完毕
      return this.en_standardization_status === 2;
    },
    is_wifi() {
      return [7, 8].includes(this.template_id);
    },
    dialogTableVisible: {
      get() {
        return this.visible;
      },
      set(v) {
        this.$emit("change", v);
      }
    },
    ref_lang_options() {
      return this.tableData.filter(
        ele =>
          ele.language !== this.edit_lang &&
          ele.status > 0 &&
          (this.is_am || ele.status >= 2)
      );
    },
    edit_lang_selected() {
      return this.edit_lang !== "";
    },
    // 展示 publish with ai 列
    // 仅需要翻译title的Template. 表示除了活動標題，其他freetext無論是否存有翻譯皆以英文填充搭配機翻按鈕發布
    // 涉及的Template: Attraction&Show, Tours&Sightseeing, Activity&Experience, Transport&Travel Service
    // 列展示条件: 有查看权限, 且属于scope范围内的template
    show_publish_with_ai_column() {
      return (
        klook.userHasPermission(
          "act/activity_list_page/am_table/language_status_publish_with_ai/view"
        ) && [1, 2, 3, 5, 7, 8, 103].includes(this.template_id)
      );
    }
  },
  watch: {
    edit_lang() {
      this.ref_lang = ""; // refresh
    },
    visible(v) {
      if (v) {
        this.tableData = [];
        // main.manageLang()
        this.initData();
      }
    }
  },
  methods: {
    async confirmOpen(edit, ref) {
      try {
        await editlock.checkLock(this.activity_id, false, null, {
          user_type: this.is_am ? 0 : 1,
          language: this.is_am ? "ALL" : edit
        });
      } catch (error) {
        // 如果活动已迁移，checkLock 会抛出 ACTIVITY_MIGRATED 异常并重定向
        if (error.message === 'ACTIVITY_MIGRATED') {
          return;
        }
        throw error;
      }
      window.localStorage.setItem("act_edit_lang", edit);
      window.localStorage.setItem("act_ref_lang", ref);
      window.open(
        klook.urlParam("lang", edit, klook.urlParam("ref", ref, this.url)),
        "_blank"
      );
    },
    async initData() {
      const resp = (await ADMIN_API.get_act_lang_status(this.activity_id))
        .result;
      const blocked_languages = await this.get_blocklist(this.activity_id);
      let { status, en_standardization_status } = resp; // 语言状态array, 以及 en_US 是否结构化完成
      // 获取语言翻译状态
      if (!this.is_edit) {
        try {
          const translate_status_obj = await this.getTranslateStatus(
            this.activity_id
          );
          status.forEach(v => {
            v.translation_status = translate_status_obj[v.language];
          });
        } catch (e) {
          console.log(e);
        }
      }

      this.blocked_languages = blocked_languages;
      this.en_standardization_status = en_standardization_status;

      // if only one language
      if (this.is_edit && status.every(item => item.status === 0)) {
        window.open(`/${KLK_LANG_PATH}act/activity/add/${this.activity_id}`);
        return undefined;
      }

      this.tableData = status;
    },
    // 获取屏蔽的语言列表
    async get_blocklist() {
      const res = await ADMIN_API.getActBlocklist(this.activity_id);
      const list = (_.get(res, "result.statuses.0.items") || []).filter(
        // =1 为被block的语言
        v => v.status === 1
      );
      return list.map(v => v.language);
    },
    // 获取语言翻译状态
    async getTranslateStatus(activity_id) {
      const res = await ADMIN_API.getActTranslateStatus(activity_id);
      const statuses = _.get(res, "result.statuses.0.items") || [];

      return statuses.reduce((acc, cur) => {
        acc[cur.language] = cur.status;
        return acc;
      }, {});
    },
    // 下面的代码来自manage_lang.vue
    // 是否是block的语言
    is_in_block_list(row) {
      return this.blocked_languages.includes(row.language);
    },
    can_pub_en(row) {
      // return !!this.tableData.find(ele => ele.language.match(/us/i) && ele.status === 3) && // has us && us already publshed
      return (
        !!this.tableData.find(
          ele => ele.language.match(/en_US/i) && ele.status === 3
        ) && // has us && us already publshed
        // !['US', 'TW', 'CN'].includes(row.language) && // current language not main language
        // !["en_US", "zh_TW", "zh_CN"].includes(row.language) && // current language not main language
        !["en_US", "zh_CN"].includes(row.language) && //! update: zh_TW can publish as en_US now
        row.status !== 3
      ); // current language not published
    },
    // 满足条件展示 publish with ai 按钮
    show_pub_ai_btn(row) {
      // 仅需要翻译title的Template. 表示除了活動標題，其他freetext無論是否存有翻譯皆以英文填充搭配機翻按鈕發布
      // 涉及的语言: 除了11种EN和zh_CN以外的其他所有多语言
      // 展示条件: 活动en_US已发布 && EN-US已经完成结构化 && 当前语言翻译状态处于"Pending"和"Done"
      // 操作条件: 有操作权限
      const arr = [
        !row.language.includes("en"), // 非英文系列
        row.language !== "zh_CN", // 非简中
        [1, 2].includes(row.translation_status), // 翻译状态为 pending || done [pending: 部分翻译完成(包括title), done: 全部翻译完成]
        !!this.tableData.find(
          ele => ele.language.match(/en_US/i) && ele.status === 3
        ), // 有美国英文且已发布
        this.is_en_standardization_finished // 英语已结构化完成
      ];
      return arr.every(v => v);
    },
    // 控制 publish with ai 按钮置灰状态
    can_operate_pub_ai_btn(row) {
      const arr = [
        klook.userHasPermission(
          "act/activity_list_page/am_table/language_status_publish_with_ai/operate"
        ), // 有操作权限
        row.default_show_english !== 1, // 未按美国英文发布
        row.status !== 3 // 未发布
      ];
      return arr.every(v => v);
    },
    fmt: lang => lang_conf.getLangObj("B_LANG", "LANG_TITLE")[lang] || lang,
    statusFmt: status => statusTable[status],
    async change_publish(
      index,
      row,
      do_publish,
      change_en_status,
      pubilsh_with_ai
    ) {
      // 取消发布 => 需要保留至少一个已发布语言
      if (!do_publish) {
        if (
          !this.tableData.find(
            ele => ele.status === 3 && ele.language !== row.language
          )
        ) {
          // can not unpub the only published lang
          // this.$alert(__("act_list_cant_not_unpub"));
          this.$message(__("act_list_cant_not_unpub"));
          return;
        }
      }
      /*
       *if (do_publish && this.act_status != 1) {
       *  this.$alert(
       *    "Can not publish language if current activity is not published"
       *  );
       *  return;
       *}
       */
      const use_en = change_en_status ? +(do_publish && row.status != 3) : ""; // only unpublished lang can be published with en.
      const data = {
        status: change_en_status ? 1 : do_publish ? 3 : 4
      };
      // 发布部分美国英文时，partial_show_english = 1 && status= 5；
      // 取消发布部分美国英文时，partial_show_english = 0 && status= 1；
      // 不是部分美国英文发布类型 partial_show_english = ""
      let pub_with_ai = "";
      // 部分发布美国英文
      if (pubilsh_with_ai) {
        pub_with_ai = +(do_publish && row.status != 3); // 1 发布部分美国英文, 0 取消发布部分美国英文
        data.status = pub_with_ai === 1 ? 5 : 1;
      }
      await ADMIN_API.change_act_lang_status(
        this.activity_id,
        row.language,
        use_en,
        data,
        pub_with_ai
      );
      // this.tableData = (await ADMIN_API.get_act_lang_status(
      //   this.activity_id
      // )).result.status;
      // 重新获取数据
      let [
        {
          result: { status = [] }
        },
        translate_status_obj
      ] = await Promise.all([
        ADMIN_API.get_act_lang_status(this.activity_id),
        getTranslateStatus(this.activity_id)
      ]);
      status.forEach(v => {
        v.translation_status = translate_status_obj[v.language];
      });
      this.tableData = status;
      this.$message(__("global_success"));
    },
    jumpToSubmit(scope) {
      window.location.href = `/${KLK_LANG_PATH}act/content/create/${
        this.activity_id
      }?lang=${scope.row.language}`;
    },
    noEditLang(row) {
      return (
        // 这里限制 BD 不可以编辑其他英文
        !this.has_em_access &&
        row.language.includes("en") &&
        row.language !== "en_US"
      );
    },
    add(i, row) {
      this.add_flag = true;
      this.edit_lang = row.language;
    },
    edit(i, row) {
      this.add_flag = false;
      this.edit_lang = row.language;
    },
    refresh() {
      this.edit_lang = this.ref_lang = "";
    },
    async confirm() {
      if (this.add_flag) {
        await ADMIN_API.change_act_lang_status(
          this.activity_id,
          this.edit_lang,
          "",
          {
            status: 1
          }
        );
      }
      this.confirmOpen(this.edit_lang, this.ref_lang);
      this.dialogTableVisible = false;
    },
    cancel() {
      this.dialogTableVisible = false;
    },
    copyReady(row) {
      return row.language.match("en") && row.status > 1;
    },
    async copyEnglish(row) {
      this.copy_from = row.language;
      this.copyEnglishDialog = true;
      await new Promise((resolve, reject) =>
        this.$on("confirm-copy", ok => (ok ? resolve() : reject()))
      );
      await ADMIN_API.copy_multi_english({
        activity_id: this.activity_id,
        data: {
          from: this.copy_from,
          target: this.copy_to
        }
      });
      klook.success();
      this.dialogTableVisible = false;
    },
    // 自定表头
    render_publish_with_ai_header(h, { column }) {
      return h("div", [
        h("span", column.label),
        h(
          "el-popover",
          {
            attrs: {
              placement: "right",
              content: this.$t("act_list_publish_with_AI_translation_intro"),
              trigger: "hover",
              width: "200"
            }
          },
          [
            h("i", {
              slot: "reference",
              class: "el-icon-information",
              style:
                "color: #888;margin-left: 5px;font-size: 14px;cursor:pointer;"
            })
          ]
        )
      ]);
    },
    // 状态展示
    getPublishStatusLabel(row) {
      if (row.default_show_english) {
        return this.$t("act_list_publishing_with_en_US");
      } else if (row.status === 5) {
        return this.$t("act_list_publishing_with_AI_translation");
      } else {
        return this.statusFmt(row.status);
      }
    },
    // 语言翻译状态展示
    getTranslationStatusLabel(row) {
      return translation_status_map[row.translation_status] || "-";
    },
    reviewEventLogs(row) {
      window.open(
        `/${KLK_LANG_PATH}act/activity/event_logs/${
          this.activity_id
        }/?language=${row.language}`,
        "_blank"
      );
    }
  }
};
</script>

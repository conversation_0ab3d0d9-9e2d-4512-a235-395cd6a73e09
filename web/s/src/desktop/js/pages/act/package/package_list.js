/*
 * jquery is error-prone,
 * better be careful !!!
 */
import { warn_message } from "../price/utils";
import { checkPassAsync } from "../components/pass-standard-confirm";

require("editable");
require("jquery.validate");
require("jquery.validate.custom");
const admin_const = require("admin_const");
const Vue = require("vue");

var main = require("../../main.js");
main.addLoading();
var lang_conf = require("lang_conf");
var actId = parseInt($("#activity_id").val());
var UNIT_PUBLISHED_NUM = {};

var langs = lang_conf.getLangObj("F_LANG", "B_LANG");
var langsObj = lang_conf.getLangObj("B_LANG", "LANG_TITLE");
var all_lang_array = lang_conf.getLangArray("B_LANG");
var editlock = require("../../../pluginit/editlock.js");

// var is_china_agent_price = window.is_china_agent_price;
var is_am = window.is_am;
var template_id;

Handlebars.registerPartial("j-selling-price", $("#j-selling-price-tpl").html());

async function getPrivsData() {
  var resp = await ADMIN_API.getActPkgPrice({
    activity_id: actId,
    params: {
      custom_language: (await ADMIN_API.getActLang({ activity_id: actId }))
        .result.language //  use published language
    }
  });
  // sort unit from published to unpublished
  if (_.isEmpty(resp.result.package_summary)) {
    window.alert(
      "A package becomes editable in package list only after its schedule has been initialized. For now, we can't find any package editable here :("
    );
    history.back();
    klook.abort();
  }
  resp.result.package_summary = resp.result.package_summary.map(pkg => {
    pkg.price = pkg.price
      .filter(unit => unit.price_value.published)
      .concat(pkg.price.filter(unit => !unit.price_value.published));
    return pkg;
  });
  // sort package sequence with published first
  resp.result.package_summary = resp.result.package_summary
    .filter(pkg => pkg.package_status === 1)
    .concat(
      resp.result.package_summary.filter(pkg => pkg.package_status !== 1)
    );
  var data = parsePackageListData(resp.result);
  data = {
    ...data,
    ...KLK_PAGE_DATA,
    publish_package_by_lang: KLK_PAGE_DATA._roles.publish_package_by_lang
  };

  renderPage(data);
}

function parsePackageListData(result) {
  var lang = langs[KLK_LANG];

  _.forEach(result.activity_summary.local, function(a) {
    if (lang == a.language) {
      result.activity_summary._local = a;
    }
  });

  //如果没有匹配到系统语言,就取简体中文显示title
  if (
    !result.activity_summary._local &&
    Array.isArray(result.activity_summary.local)
  ) {
    result.activity_summary._local = result.activity_summary.local[0];
  } else {
    result.activity_summary._local = {};
  }

  _.forEach(result.package_summary, function(pkg_data) {
    pkg_data._price_info = [];
    pkg_data._package_name = "";

    // a.is_add_on =
    //   a.package_type ? __("main_package") : __("content_add_on_package");
    pkg_data.is_add_on = !pkg_data.main_package_id
      ? __("main_package")
      : __("merge_package");
    if (pkg_data.merchant_info) {
      pkg_data.merchant =
        pkg_data.merchant_info.merchant_id +
        "-" +
        pkg_data.merchant_info.merchant_name;
      let o = pkg_data.merchant_info.operational_db
        ? `Responsible BD 2: ${pkg_data.merchant_info.operational_db}`
        : "";
      let c = pkg_data.merchant_info.commercial_db
        ? `Responsible BD 1: ${pkg_data.merchant_info.commercial_db}`
        : "";
      pkg_data.merchant_owner_info = `${c}<br/>${o}`;
    }
    pkg_data.responsible_bd = result.activity_summary.responsible_bd;

    pkg_data._package_pricing_model =
      pkg_data.package_pricing_model === 1
        ? __("pm_price_calendar")
        : __("pm_fixed_price");
    pkg_data._package_name = pkg_data.package_local.some(
      v => v.language_type == "en_US"
    )
      ? pkg_data.package_local.find(v => v.language_type == "en_US").name
      : pkg_data.package_local[0].name;

    switch (pkg_data.spec_status) {
      case 0: {
        pkg_data._specs_status = `<span style="color: #ff5722;">${__(
          "act_em_specs_status_no"
        )}</span>`;
        break;
      }
      case 1: {
        pkg_data._specs_status = `<span style="color: #02ac98;">${__(
          "act_em_specs_status_matched"
        )}</span>`;
        break;
      }
      case 2: {
        pkg_data._specs_status = `<span style="color: #ffa628;">${__(
          "act_em_specs_status_pending"
        )}</span>`;
        break;
      }
    }

    pkg_data.price.forEach(function(b) {
      // Sort the unit name by B_LANG sequence
      b.price_local = sortUnitName(b.price_local);
      let c = (b.price_local || [])[0];
      if (c) {
        c._price_value = b.price_value;
        c._main_info = pkg_data;
        pkg_data._price_info.push(c);
      }
      if (b.price_value.published) {
        if (typeof UNIT_PUBLISHED_NUM[pkg_data.package_id] === "number") {
          UNIT_PUBLISHED_NUM[pkg_data.package_id] += 1;
        } else {
          UNIT_PUBLISHED_NUM[pkg_data.package_id] = 1;
        }
      }
    });
  });

  return result;
}

function sortUnitName(data, field = "language_type") {
  let B_LANG = lang_conf.getLangArray("B_LANG");
  return _.sortBy(data, function(v) {
    return B_LANG.indexOf(v[field]);
  });
}

function renderPage(data) {
  data.actId = actId;
  data.KLK_LANG_PATH = KLK_LANG_PATH;
  data.is_am = is_am;
  data.is_spu = admin_const.spu_template_ids.includes(template_id);
  data.canEditSpecs =
    window.KLK_PAGE_DATA._roles.canEnterCategoryAttributes &&
    !admin_const.spu_template_ids.includes(template_id); // wifi ysim sim

  // var html = klook.render($('#module-privs-tr').html(), data);

  let html;
  // regardless of role
  if (window.is_em) {
    html = klook.render($("#module-privs-tr-content").html(), data);
    $("#module-privs-table-content tbody").html(html);
  }
  if (window.is_am) {
    html = klook.render($("#module-privs-tr").html(), data);
    $("#module-privs-table tbody").html(html);
  }
  if (window.is_gds) {
    html = klook.render($("#module-privs-tr-gds").html(), data);
    $("#module-privs-table-gds tbody").html(html);
  }

  $("#activity_name span").text(data.activity_summary._local.title); // FIXME
  $("#add-new-box").removeClass("hidden");
  initEditSort();
}

// octave
$(".table-list").on("click", 'a[data-role="changePublished"]', function() {
  var $tr = $(this).closest("tr"),
    data = JSON.parse($tr.attr("data-args"));
  data.package_name = $tr.data("package-name");
  var package_id = $tr.data("package-id");
  if (data.price_type === 0) {
    klook.popupAlert(__("first_sku_id_no_delete"));
    return false;
  }
  var sku_id = data.sku_id;
  var doPublish = $(this).data("value") == "1";
  if (doPublish === true) {
    if (UNIT_PUBLISHED_NUM[package_id] > Number.MAX_SAFE_INTEGER) {
      klook.popupAlert(__("package_price_units_overflow"));
      return false;
    }
  }
  var postBody = {
    sku_id: sku_id,
    published: doPublish
  };
  var _resultPromise = doPublish
    ? Promise.resolve()
    : main.checkActivityInfo(sku_id, "sku");
  _resultPromise
    .then(async () => {
      if (!doPublish) {
        // 下架活动、套餐、sku的时候需要检查是否是 pass standard
        const checkPassRes = await checkPassAsync({ sku_ids: sku_id });

        if (checkPassRes && checkPassRes.stop) {
          return;
        }

        postBody.reason = checkPassRes.reason;
        postBody.reason_code = checkPassRes.reason_code;
        postBody.force = !!checkPassRes.force;
      }
      let resp = await ADMIN_API.pkgSkuPublish({
        data: postBody,
        just_throw: false
      });

      if (resp.success) {
        klook.popupAlert(__("global_success"));
        window.location.reload(true);
      } else {
        klook.popupAlert(__("global_error"));
      }
    })
    .catch(err => {
      console.log(err);
      klook.popupAlert(err);
    });
});
$(".table-list").on("click", 'a[data-role="editPrice"]', async function() {
  try {
    await editlock.checkLock(actId, false);
  } catch (error) {
    // 如果活动已迁移，checkLock 会抛出 ACTIVITY_MIGRATED 异常并重定向
    if (error.message === 'ACTIVITY_MIGRATED') {
      return;
    }
    throw error;
  }

  var $tr = $(this).closest("tr"),
    data = JSON.parse($tr.attr("data-args")),
    auth = $(this).attr("data-auth"),
    role;

  if (auth === "is_gds") {
    role = "gds";
  } else if (auth === "is_bd") {
    role = "bd";
  } else {
    window.alert("Error auth.");
    throw new Error("Error auth.");
  }

  let search = new URLSearchParams(location.search);
  search.set("sku_id", data.sku_id);

  if (auth === "is_gds") {
    window.location.href = `/${KLK_LANG_PATH}act/activity/${actId}/price/${
      data.package_id
    }/agent_modify?${search}`;
    return;
  }
  window.location.href = `/${KLK_LANG_PATH}act/activity/${actId}/price/${
    data.package_id
  }/modify_${role}?${search}`;

  $("#cost").on("keyup", function() {
    main.input2point($(this));
  });

  $(".edit-price-box").on("click", ".glyphicon-minus-sign", function() {
    var $parent = $(this).closest(".j_selling_price_row");

    //手动添加的 点击删除按钮后 直接删除，原来存在的 直接隐藏
    if ($parent.attr("data-add")) {
      $parent.remove();
    } else {
      $parent.addClass("hidden");
    }

    // //same day booking price  直接删除
    // if ($parent.attr('data-price-type') == 1) {
    //     $parent.remove();
    // }
  });

  $(".edit-price-box").on("click", ".glyphicon-plus-sign", function() {
    var $parent = $(this).closest("[data-role=j_price_box]"),
      price_type = parseInt($parent.attr("data-type"));

    var html = klook.render(
      $("#j-selling-price-tpl").html(),
      $.extend(KLK_PAGE_DATA, {
        isAdd: 1,
        price_type: price_type
      })
    );

    $parent.find(".j_selling_price").append(html);

    setInputId();
    validateForm(data);
  });

  $(".j_currency_select").each(function() {
    var val = $(this).attr("data-value");

    $(this)
      .find("option")
      .each(function() {
        if ($(this).text() == val) {
          $(this).prop("selected", "selected");
        }
      });
  });

  setInputId();
  validateForm(data);
});

function setInputId() {
  $(".selling_price_input").each(function(i) {
    $(this).attr("id", "selling_price_input_" + i);
  });
}

$(".add-new").on("click", function() {
  window.location.href = main.setQuery(
    {
      package_type: 1,
      activity_id: actId,
      package_id: "null",
      lang: "en_US"
    },
    main.setPathname(`/act/package/info`)
  );
});
$(".change-pricing-model").on("click", function() {
  window.location.href =
    `/${KLK_LANG_PATH}act/package/change_pricing_model/${actId}` +
    window.location.search;
});
$(".add-on-new").on("click", function() {
  window.location.href = main.setQuery(
    {
      package_type: 1,
      activity_id: actId,
      package_id: "null"
    },
    main.setPathname(`/act/package/info`)
  );
});

$(".table-list").on("click", ".act-role-link", function() {
  var role = $(this).attr("data-role"),
    $tr = $(this).closest("tr"),
    data = JSON.parse($tr.attr("data-args")),
    package_id = data.package_id,
    sku_id = data.sku_id;

  data.package_name = $tr.data("package-name");
  /*get auto publish data*/
  let auto_publish = {
    auto_published: data.auto_published,
    auto_publish_published: data.auto_publish_published,
    auto_publish_published_time: data.auto_publish_published_time,
    auto_publish_unpublished: data.auto_publish_unpublished,
    auto_publish_unpublished_time: data.auto_publish_unpublished_time
  };

  switch (role) {
    case "publish":
      rolePublish(package_id, true, auto_publish);
      break;
    case "unpublish":
      rolePublish(package_id, false, auto_publish);
      break;
    case "delete":
      roleDelete(package_id);
      break;
    case "edit-pkgname":
      editName(package_id);
      break;
    case "edit-specs":
      location.href = main.setPathname(`act/${actId}/specs/list`);
      break;
    case "event-log":
      window.location.href = main.setQuery(
        {
          package_id: package_id
        },
        main.setPathname(`act/activity/event_logs/${actId}`)
      );
      break;
    case "event-unit-log":
      window.location.href = main.setQuery(
        {
          package_id: package_id,
          sku_id: sku_id
        },
        main.setPathname(`act/activity/event_logs/${actId}`)
      );
      break;
  }
});
$(".table-list").on("click", 'a[data-role="manage_lang"]', function() {
  let pkg_id = JSON.parse(
    $(this)
      .closest("tr")
      .attr("data-args")
  ).package_id;
  window.vueapp.id = pkg_id;
  window.vueapp.dialogTableVisible = true;
});

$(".table-list").on("click", 'a[data-role="edit-pkg"]', async function(e) {
  e.preventDefault(); // stop a link

  // manageLang function may be open am page('/act/activity/add'), so should lock it.
  if (window.is_am) {
    try {
      await editlock.checkLock(actId, false, null, {
        user_type: 0,
        language: "ALL"
      });
    } catch (error) {
      // 如果活动已迁移，checkLock 会抛出 ACTIVITY_MIGRATED 异常并重定向
      if (error.message === 'ACTIVITY_MIGRATED') {
        return;
      }
      throw error;
    }
  }

  var url = $(this).attr("href");
  main.manageLang({ id: actId, is_edit: true, url, template_id });
});

async function editName(package_id) {
  var dialog = klook.popupCustomDialog({
    title: __("edit_pacakge_name"),
    width: 400,
    content: __("global_show_msg_loading"),
    ok: async function() {
      var data = [];

      var dataItem = {
        package_id: package_id,
        package_details: []
      };

      $('input[data-role="local"]').each(function() {
        var $this = $(this);

        dataItem.package_details.push({
          language_type: $this.attr("id"),
          name: $this.val(),
          desc: $this.attr("desc")
        });
      });

      data.push(dataItem);

      // klook.ajaxPostJSON(klook.parseAjaxUrl('/prosrv/packages/locale/update'), data, function(resp) {
      //     if (resp.success) {
      //         klook.showMsgAndGo(__('global_show_msg_edit_success'), function() {
      //             window.location.reload()
      //         });
      //     }
      // });

      await ADMIN_API.pkgLocaleUpdate({
        data
      });

      klook.showMsgAndGo(__("global_show_msg_edit_success"), function() {
        window.location.reload();
      });

      return false;
    }
  });

  var resp = await ADMIN_API.getPkg({ package_id });
  resp.result.deps = main.deps;
  var html = klook.render($("#edit-package-name-tmpl").html(), resp.result);
  dialog.content(html);
  dialog.show();
}

function roleDelete(package_id) {
  klook.popupDialog(
    __("js_confirm_delete") + "id: " + package_id,
    async function() {
      // klook.ajaxPost(klook.parseAjaxUrl('/prosrv/packages/' + package_id + '/destroy'), function(resp) {
      //     if (resp.success) {
      //         window.location.reload();
      //     }
      // });
      await ADMIN_API.pkgDestroy({
        package_id
      });

      window.location.reload();
    }
  );
}

function rolePublish(package_id, type, auto_publish) {
  var data = {
    package_id: package_id,
    published: type
  };
  /*generate specific popup string base on status of auto_publish*/
  let popupStr = klook.strformat(
    __("confirm_publish_pkg"),
    type ? __("global_publish_1") : __("global_unpublish_1"),
    package_id
  );
  let auto_published_str = "";
  let time_str = "";
  let publish_str = "";
  let title = type ? "Publish" : "Unpublish";
  if (auto_publish) {
    if (type && auto_publish.auto_publish_published) {
      auto_published_str = "auto-published";
      time_str = auto_publish.auto_publish_published_time;
      publish_str = "publish";
      popupStr = `The package has set to be ${auto_published_str} by ${time_str}, are you sure to ${publish_str} it now?`;
    }
    if (!type && auto_publish.auto_publish_unpublished) {
      auto_published_str = "auto-unpublished";
      time_str = auto_publish.auto_publish_unpublished_time;
      publish_str = "unpublish";
      popupStr = `The package has set to be ${auto_published_str} by ${time_str}, are you sure to ${publish_str} it now?`;
    }
  }
  klook.popupDialog(
    popupStr,
    function() {
      (type
        ? Promise.resolve()
        : main.checkActivityInfo(package_id, "package")
      ).then(async () => {
        // await klook.pmsPostJSON(klook.parseAjaxUrl('/prosrv/packages/published'), data)
        if (!type) {
          // 下架活动、套餐、sku的时候需要检查是否是 pass standard
          const checkPassRes = await checkPassAsync({
            package_ids: package_id
          });

          if (checkPassRes && checkPassRes.stop) {
            return;
          }

          data.reason = checkPassRes.reason;
          data.reason_code = String(checkPassRes.reason_code);
          data.force = !!checkPassRes.force;
        }
        let resp = await ADMIN_API.pkgPublished({
          data,
          just_throw: false
        });
        if (resp.success) {
          klook.showMsgAndGo("success", function() {
            window.location.reload();
          });
        } else {
          alert(resp.error.message);
          window.location.reload();
        }
      });
    },
    { title }
  );
}

function initEditSort() {
  $(".editable").each(function() {
    $(this).editable({
      pk: 1,
      url: klook.parseAjaxUrl("/prosrv/packages/sort"),
      ajaxOptions: {
        type: "POST",
        dataType: "json",
        contentType: "application/json; charset=utf-8",
        headers: klook.getCommonHeader()
      },
      validate: function(value) {
        if ($.trim(value) == "") {
          return __("global_validate_text_required");
        }
        if (!/^\d+$/.test($.trim(value))) {
          return __("global_validate_number_required");
        }
      },
      params: function(params) {
        var $tr = $(this).closest("tr");

        $tr.attr("data-order", parseInt($(".editableform input").val() || 0));

        var data = [];
        $('#module-privs-table tr[data-tr="pkg"]').each(function() {
          data.push({
            package_id: parseInt($(this).attr("data-package-id")),
            priority: parseInt($(this).attr("data-order"))
          });
        });

        return JSON.stringify(data);
      },
      success: function(resp, newValue) {
        if (!resp.success) {
          klook.popupAlert(resp.error.message);
          return false;
        } else {
          window.location.reload();
        }
      }
    });
  });
}

function getCustomData() {
  var data = [],
    cur_sell_currency = $("#cur_sell_currency").val();

  $(".j_selling_price_row").each(function() {
    var $this = $(this),
      item = {};
    item.currency = $this.find(".j_currency_select option:selected").text();
    item.amount = +$this.find(".selling_price_input").val();

    if ($this.hasClass("hidden")) {
      item.flag = 1;
    } else {
      item.flag = 0;
    }
    item.type = parseInt($(this).attr("data-price-type"));
    item.is_default = cur_sell_currency == item.currency;

    data.push(item);
  });

  return data;
}

function validateForm(data) {
  $("#validatorForm").validate({
    focusInvalid: false,
    errorElement: "div",
    errorClass: "err-tip",
    errorPlacement: function(error, element) {
      $(element)
        .closest(".form-validate-box")
        .append($(error));
    },
    rules: {
      cost: {
        required: true,
        number: true
      },
      retail: {
        required: true,
        number: true
      },
      selling: {
        required: true,
        number: true
      },
      regular: {
        required: true,
        number: true
      },
      premier: {
        required: true,
        number: true
      },
      selling_price_input: {
        required: true,
        number: true
      }
    },
    messages: {
      cost: __("global_validate_number_required"),
      retail: __("global_validate_number_required"),
      selling: __("global_validate_number_required"),
      regular: __("global_validate_number_required"),
      selling_price_input: __("global_validate_number_required"),
      premier: __("global_validate_number_required")
    },
    highlight: function(e) {
      $(e)
        .closest(".form-group")
        .addClass("has-error");
    },
    success: function(e) {
      $(e)
        .closest(".form-group")
        .removeClass("has-error");
    },
    submitHandler: async function(form) {
      var cost = parseFloat($.trim($("#cost").val()));
      var retail = parseFloat($.trim($("#retail").val()));
      var selling = parseFloat($.trim($("#selling").val()));
      var regular = parseFloat($.trim($("#regular").val()));
      var premier = parseFloat($.trim($("#premier").val()));
      var china = parseFloat($.trim($("#china").val() || -1));

      var postData = {
        sku_id: data.sku_id,
        cost,
        retail,
        selling,
        regular,
        premier,
        china
      };

      /*
       * validate price values !!!
       * be careful with PRICE !!!
       */
      Object.values(postData).forEach(v => {
        if (!_.isNumber(v)) {
          window.alert("Invalid Price!!!");
          klook.abort();
        }
      });

      var cur_sell_currency = $("#cur_sell_currency").val();

      postData.custom = getCustomData();
      var sameDayArr = postData.custom.filter(function(x) {
        return x.type == 1 && x.flag == 0;
      });

      if (
        sameDayArr.length &&
        !sameDayArr.filter(function(x) {
          return x.currency == cur_sell_currency;
        }).length
      ) {
        klook.popupAlert(__("same_day_booking_price_tip", cur_sell_currency));
        return;
      }

      // klook.ajaxPostJSON(klook.parseAjaxUrl('/prosrv/prices/value/update'), postData, function(resp) {
      //     main.changePriceError(resp);
      //     if (resp.success) {
      //         window.location.reload()
      //     }
      // });

      let resp = await ADMIN_API.pkgPricesValueUpdate({
        data: postData,
        just_throw: false
      });
      //await main.changePriceError(resp);
      await warn_message(this, resp.result);
      resp.success && window.location.reload();
    }
  });
}

// below @octave
async function getPosition() {
  template_id = (await klook.pmsGet(
    klook.parseAjaxUrl(`/prosrv/activities/${actId}/language`)
  )).result.template_id; // get edit lang and template id
  var resp = await klook.pmsGet(
    klook.parseAjaxUrl("/prosrv/activities/" + actId + "/position")
  );
  if (
    !resp.result.main_package ||
    resp.add_on_package === 1 ||
    admin_const.spu_template_ids.includes(template_id)
  ) {
    $(".add-new, .add-on-new").hide();
  }
}
function initManageLang() {
  window.vueapp = new Vue({
    el: "#manage-language-table",
    data() {
      return {
        dialogTableVisible: false,
        tableData: [],
        id: ""
      };
    },
    computed: {
      title() {
        return "Manage language status(Package ID" + this.id + ")";
      }
    },
    watch: {
      dialogTableVisible(val) {
        if (val) {
          this.getTableData();
        }
      }
    },
    methods: {
      async getTableData() {
        let params = {
          language_list: all_lang_array,
          package_id_list: [this.id]
        };
        let res = (await ADMIN_API.managePkgLang(params)).result.statuses[0]
          .items;
        this.tableData = res.map(i => {
          i.languageText = langsObj[i.language];
          i.statusText = i.status
            ? this.$t("package_unit_publish")
            : this.$t("package_unit_unpublish");
          return i;
        });
      },
      async publish(type, data) {
        let params = {
          package_id: this.id,
          language: data.language,
          status: type === "publish" ? 1 : 0
        };
        await ADMIN_API.publishPkgLang(params);
        this.$message.success("Success");
        this.getTableData();
      }
    }
  });
}
(async function() {
  await getPosition();
  await getPrivsData();
  $(".add-on-new").hide(); // temporary disable addon
  initManageLang();
})();

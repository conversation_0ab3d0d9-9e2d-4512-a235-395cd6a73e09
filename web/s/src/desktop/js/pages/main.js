require("bootstrapTable");
var lang_conf = require("lang_conf");
var mandatoryLang = "en";
var mandatoryLangtype = "en_US";
var editlock = require("../pluginit/editlock.js");
const URI = require("urijs");

const Vue = require("vue");
const Vlocale = require("../libs/vue-locale.js");
const manage_lang = require("./act/manage_lang.vue").default; // FIXME can not use import in a module using module.exports
console.assert(manage_lang);
Vlocale.prepareElementLocale();
/*
 * bootstrap-table - v1.10.1 - 2016-02-17
 * https://github.com/wenzhixin/bootstrap-table
 * Copyright (c) 2016 zhixin wen
 * Licensed MIT License
 */
!(function(a) {
  "use strict";
  (a.fn.bootstrapTable.locales["zh-CN"] = {
    formatLoadingMessage: function() {
      return __("global_bts_table_formatLoadingMessage");
    },
    formatRecordsPerPage: function(a) {
      // return "每页显示 " + a + " 条记录"
      return klook.strformat(__("global_bts_table_formatRecordsPerPage"), a);
    },
    formatShowingRows: function(a, b, c) {
      // return "显示第 " + a + " 到第 " + b + " 条记录，总共 " + c + " 条记录"
      return klook.strformat(__("global_bts_table_formatShowingRows"), c);
    },
    formatSearch: function() {
      return __("global_bts_table_formatSearch");
    },
    formatNoMatches: function() {
      return __("global_bts_table_formatNoMatches");
    },
    formatPaginationSwitch: function() {
      return __("global_bts_table_formatPaginationSwitch");
    },
    formatRefresh: function() {
      return __("global_bts_table_formatRefresh");
    },
    formatToggle: function() {
      return __("global_bts_table_formatToggle");
    },
    formatColumns: function() {
      return __("global_bts_table_formatColumns");
    }
  }),
    a.extend(
      a.fn.bootstrapTable.defaults,
      a.fn.bootstrapTable.locales["zh-CN"]
    );
})(jQuery);

// @dep by wifi udpate
// async function checkFnbMenu(activity_id) {
// var template = (await klook.pmsGet(klook.parseAjaxUrl(`/prosrv/activities/${activity_id}/language`))).result.template_id
// if (template == 4) {
// $(".fnb_menu_li").show()
// }
// }

var dotCurrency = ["IDR", "JPY", "KRW", "LAK", "TWD", "VND"];

function hasParameter(url) {
  url = url || "";
  if (url.indexOf("?") > -1) {
    return url + "&";
  } else {
    return url + "?";
  }
}
// layout
function toggleSlider() {
  var $containerBox = $("#container-box"),
    $main = $containerBox.find(".main"),
    $icon = $(".open-menu");
  if ($containerBox.hasClass("pack-up")) {
    $containerBox.removeClass("pack-up");
    $main.addClass("col-sm-9 col-sm-offset-3 col-md-10 col-md-offset-2 main");
    $icon.removeClass("rotate");
    $main.css("margin-left", "0 !important");
  } else {
    $containerBox.addClass("pack-up");
    $main.removeAttr("class").addClass("col-md-12 main");
    $icon.addClass("rotate");
    $main.css("margin-left", "0 !important");
  }
}

function setSessionStorage(key, value) {
  if (window.sessionStorage) {
    sessionStorage.setItem(key, JSON.stringify(value));
  } else {
    klook.popupAlert("浏览器不支持sessionStorage！请更换先进的浏览器！");
  }
}

function initUI() {
  var winH = $(window).height();
  $(".container-fluid-con").css("min-height", winH - 80);

  $(".nav-sidebar li").on("click", function() {
    $(this)
      .removeClass("li-close")
      .siblings()
      .addClass("li-close");
  });

  if (KLK_ENV != "prd") {
    $(".logo img").attr("src", "");
  } else {
    $(".logo img").attr(
      "src",
      "https://cdn.klook.com/s/dist_web/klook-admin-projects/klook-admin/s/dist/assert/desktop/imgs/klookteam-logo.png"
    );
  }
}

//
function getLangTextBylang(lang) {
  //处理gulp dev
  var __d = __;
  return __d("act_lan_" + lang);
}

//如果传了 langs[], 根据langs里面的输出option
function getAllLangOption(langs) {
  var lang_info = lang_conf.getLangObj("B_LANG", "B_LANG"),
    arr = [];

  for (var key in lang_info) {
    arr.push({
      get country_code() {
        console.error(`Use country_code on getAllLangOption: Func`);
        return "country_code";
      },
      // country_code: lang_info[key] || '',
      B_LANG: key,
      options: getLangTextBylang(key) // change COUNTRY_CODE to B_LANG(: lang_info[key])
    });
  }

  if (langs) {
    var _arr = [];
    langs.forEach(function(lang) {
      arr.forEach(function(ele) {
        if (ele.B_LANG == lang) {
          _arr.push(ele);
        }
      });
    });

    return _arr;
  }

  return arr;
}

initUI();

function bestMatchLang(nameLiteral, langLiteral, array) {
  // multilauguage array ==> single lang string  #Octave
  if (_.isEmpty(array)) {
    return "";
  }

  return _.get(
    array.find(v => v[langLiteral] == getEditLang()) || array[0],
    nameLiteral,
    ""
  );
}

Handlebars.registerHelper({
  __: __,
  t: __,
  getLangTextBylang: getLangTextBylang,
  and: (a, b) => a && b,
  or: (a, b) => a || b,
  eq: function(v1, v2) {
    return v1 === v2;
  },
  not: function(v1) {
    return !v1;
  },
  noteq: function(v1, v2) {
    return v1 !== v2;
  },
  lt: function(v1, v2) {
    return v1 < v2;
  },
  gt: function(v1, v2) {
    return v1 > v2;
  },
  formatCurrency: function(currency, price) {
    return klook.isCurrencyInt(currency) ? price : price.toFixed(2);
  },
  inc: function(value, options) {
    return parseInt(value) + 1;
  },
  evl: function(exps, options) {
    var test_result = eval(exps);
    if (test_result) {
      return options.fn(this);
    } else {
      return options.inverse(this);
    }
  },
  split: function(opt, options) {
    var val = opt.split("/");
    var content = "";
    val.forEach(function(a, b) {
      content += "<option value='" + b + "'>" + a + "</option>";
    });
    return new Handlebars.SafeString(content);
  },
  addOne: function(index) {
    this._index = index + 1;
    return this._index;
  },
  showDiscount: function(selling, retail) {
    var t;
    t = (1 - selling / retail) * 100;
    return t.toFixed(0) + "%";
  },
  bestMatchLang,
  parseDotPrice: function(price, currency) {
    return parseDotPrice(price, currency);
  },
  JSON: function(obj) {
    return JSON.stringify(obj);
  }
});

function checkHide(checkbox, ele, callback) {
  $(".main")
    .off("click")
    .on("click", checkbox, function() {
      $(this).toggleClass("active");
      if ($(this).hasClass("active")) {
        $(this)
          .parents(".checkbox")
          .siblings(ele)
          .show();
      } else {
        $(this)
          .parents(".checkbox")
          .siblings(ele)
          .hide();
      }
      if (callback) {
        callback();
      }
    });
}

(function() {
  $(".open-menu").on("click", function() {
    toggleSlider();
  });
  $("#log-out").click(function() {
    klook.logout({ redirect_url: window.location.href });
  });
  $("#crawler-log-out").click(function() {
    klook.crawlerLogout();
  });
  var url = window.location.href;
  if (url.indexOf("zh-CN") > -1) {
    $("#language-btn").val("zh-CN");
  } else if (url.indexOf("zh-TW") > -1) {
    $("#language-btn").val("zh-TW");
  }
  $("#language-btn").change(function() {
    var val = $(this).val(),
      url = window.location.pathname.replace(/(zh-CN\/|zh-TW\/)/gi, ""),
      host = window.location.host,
      search = window.location.search,
      protocol = window.location.protocol,
      newUrl =
        protocol + "//" + host + (val == "en" ? "" : "/" + val) + url + search;
    window.location.href = newUrl;
    $.cookie("klk_lang", val, {
      path: "/"
    });
  });
  // var ready;
  // $(window).scroll(function () {
  //   if ($(document).scrollTop() > 50) {
  //     if (!ready) {
  //       $(".navbar-fixed-top").css("margin-top", "-50px");
  //       $(".sidebar").css("top", "30px");
  //       ready = true;
  //     }
  //   } else {
  //     $(".navbar-fixed-top").css("margin-top", "0");
  //     $(".sidebar").css("top", "79px");
  //     ready = false;
  //   }
  // });
})();

// dep for maintainance
// function unloadAction(isChange) {
//     return (function() {
//         if (isChange) {
//             if (!confirm(__("act_leave_tips"))) {
//                 isChange = false;
//             }
//             return true;
//         } else {
//             return undefined;
//         }
//     })();
// }

window.edit_status = 0;

//countryOnly: true 只显示国家，不显示城市
async function initCountryAndCity(cb, countryOnly) {
  var city_tpl =
      ' <option value="">{{t "global_select"}}</option>{{#each cities}}<option value="{{city_id}}">{{city_name}}</option> {{/each}}',
    country_tpl =
      '<option value="0">{{t "global_select"}}</option>{{#each info}}<option value="{{country_id}}">{{country_name}}</option>{{/each}}';

  var city_info;

  function renderCity(country_id) {
    var cityArr = _.find(city_info, function(a) {
      return a.country_id == country_id;
    });

    var html = klook.render(city_tpl, cityArr);
    $('select[data-role="city-select"]').html(html);
  }

  function renderCountry(resp) {
    city_info = resp.result.info;

    var html = klook.render(country_tpl, resp.result);

    $('select[data-role="country-select"]')
      .html(html)
      .change(function() {
        if (!countryOnly) {
          var country_id = $(this).val();
          renderCity(country_id);
        }
      });

    cb && cb();
  }

  if (window._countryArr) {
    renderCountry(window._countryArr);
  } else {
    let resp = await ADMIN_API.getCities({
      just_throw: false
    });

    if (resp.success) {
      window._countryArr = resp;
      renderCountry(resp);
    } else {
      klook.popupAlert(resp.error.message);
    }
  }
}

function isActive(seconds) {
  seconds = seconds || 30;
  seconds = parseInt(seconds);
  var timer;
  window._edit_ing = true;

  $(document).on("keydown click", function() {
    window._edit_ing = true;
    clearInterval(timer);
    beginCount();
  });

  function beginCount() {
    timer = setInterval(function() {
      window._edit_ing = false;
      //klook.popupAlert(seconds+"\u79d2\u5185\u4f60\u6ca1\u6709\u8fdb\u884c\u4efb\u4f55\u64cd\u4f5c,\u4f60\u5df2\u7ecf\u5904\u4e8e\u4e0d\u6d3b\u8dc3\u72b6\u6001\uff01");
    }, seconds * 1000);
  }
  beginCount();
}

function chkAllSize(name) {
  var $chkAll = $('input[data-rel="' + name + '"]');
  if (
    $('input[name="' + name + '"]').length ==
    $('input[name="' + name + '"]:checked').length
  ) {
    $chkAll.prop("checked", true);
  } else {
    $chkAll.prop("checked", false);
  }
}

//当前全选按钮的jQuery对象
function chkAll(obj) {
  var rel = obj.attr("data-rel"),
    $rels = $('input[name="' + rel + '"]');

  if (obj.prop("checked")) {
    $rels.prop("checked", true);
  } else {
    $rels.prop("checked", false);
  }
}

function isRepeat(arr) {
  var hash = {};
  for (var i in arr) {
    if (hash[arr[i]]) {
      return true;
    }
    // 不存在该元素，则赋值为true，可以赋任意值，相应的修改if判断条件即可
    hash[arr[i]] = true;
  }
  return false;
}

/*
 * DRY:
 * `getRefLang` & `getEditLang` are two unified methods to get ref/edit language from url or localStorage, don't repeat;
 */

function verifyLang(lang) {
  if (lang && !lang_conf.getLangArray("B_LANG").includes(lang)) {
    window.alert("Error: language format invalid");
    window.history.back();
    return;
  }
  return lang;
}

function getRefLang() {
  var lang = new URLSearchParams(location.search).get("ref");
  return verifyLang(lang);
}

function getEditLang() {
  var lang = new URLSearchParams(location.search).get("lang") || "en_US";
  return verifyLang(lang);
}

function getUserLangs() {
  return [
    ...new Set([
      getEditLang(),
      ...(getRefLang() ? [getRefLang()] : []),
      "en_US",
      lang_conf.getLangObj("F_LANG", "B_LANG")[window.KLK_LANG]
    ])
  ];
}

function initFilterLan(lang_created) {
  if (!Array.isArray(lang_created) || !lang_created.length) {
    console.warn("Warning : No available language for current activity!!");
    return [];
  }

  var url = new URL(location.href);
  var search = new URLSearchParams(location.search);
  var refLang = getRefLang();
  var editLang = getEditLang();

  if (!lang_created.includes(editLang)) {
    editLang = lang_created[0];
    search.set("lang", editLang);
  }
  if (!lang_created.includes(refLang)) {
    refLang = "";
    search.set("ref", refLang);
  }
  search.has("ref") || search.set("ref", refLang);
  search.has("lang") || search.set("lang", editLang);
  url.search = search;
  window.history.replaceState(null, null, url);

  return refLang ? [editLang, refLang] : [editLang];
}

function translators(language) {
  var reflang = [getRefLang()];
  if (!reflang) {
    return false;
  }
  var editLang = [getEditLang()];
  if (editLang.indexOf(mandatoryLangtype) === -1) {
    editLang.push(mandatoryLangtype);
  }

  reflang.forEach(function(n, i) {
    if (!n) {
      return false;
    }
    var a = n;
    var b = n.toUpperCase();
    $(".activity-input input." + a).prop("disabled", true);
    $(".edit-con>div." + a).addClass("disabled");
    $(".activity-input input." + b).prop("disabled", true);
    $(".edit-con>div." + b).addClass("disabled");
  });
  var showLan = reflang.concat(editLang);
  var hideLan = [];
  language.forEach(function(n, i) {
    if (showLan.map(x => x).indexOf(n) == -1) {
      hideLan.push(n);
      var a = n;
      var b = n.toUpperCase();
      $(".activity-input input." + a).hide();
      $(".edit-con>div." + a).hide();
      $(".activity-input input." + b).hide();
      $(".edit-con>div." + b).hide();
    }
  });
}

async function initLang(activity_id, callback) {
  // FIXME
  // var resp = await klook.pmsGet(klook.parseAjaxUrl(`/prosrv/activities/${activity_id}/language`))
  var resp = await ADMIN_API.getActLang({
    activity_id
  });
  if (resp.result.language) {
    // language can be `[]`, but not `null`
    if (callback instanceof Function) {
      callback(resp);
    }
  }
  return resp;
}

async function initBannerCountry(cb) {
  var $country = $('select[data-role="country-select-code"]');
  var country_tpl =
    '{{#each this}}<option value="{{code}}">{{name}}</option>{{/each}}';

  // klook.ajaxGet(klook.parseAjaxUrl("/opersrv/ads/get/country"), function(resp) {
  //     var template = Handlebars.compile(country_tpl);
  //     $country.html(template(resp.result));
  //     cb && cb();
  // });
  let resp = await ADMIN_API.adsGetCountry({
    just_throw: false
  });

  var template = Handlebars.compile(country_tpl);
  $country.html(template(resp.result));
  cb && cb();
}

async function initCities(cb) {
  var $city = $('select[data-role="cities-select"]');
  var city_tpl =
    '{{#each this}}<option value="{{city_id}}">{{city_name}}</option> {{/each}}';

  // klook.ajaxGet(klook.parseAjaxUrl("/opersrv/cities"), function(resp) {
  //     if (resp.success) {
  //         var cities = [];
  //
  //         resp.result.items.forEach(function(a) {
  //             cities = cities.concat(a.cities);
  //         });
  //
  //         var template = Handlebars.compile(city_tpl);
  //         $city.html(template(cities));
  //         cb && cb();
  //     } else {
  //         klook.popupAlert(resp.error.message);
  //     }
  // });

  let resp = await ADMIN_API.getOpersrvCities({
    just_throw: false
  });

  if (resp.success) {
    var cities = [];

    resp.result.items.forEach(function(a) {
      cities = cities.concat(a.cities);
    });

    var template = Handlebars.compile(city_tpl);
    $city.html(template(cities));
    cb && cb();
  } else {
    klook.popupAlert(resp.error.message);
  }
}

function btnLoading(o) {
  o.button("loading");
  setTimeout(function() {
    o.button("reset");
  }, 5000);
}

function initCountryAndCitySelect() {
  var country_tpl =
      '<option value="0">' +
      __("global_select") +
      '</option>{{#each this}}<option value="{{country_id}}">{{country_name}}</option> {{/each}}',
    city_tpl =
      '<option value="">' +
      __("global_select") +
      '</option>{{#each this}}<option value="{{city_id}}">{{city_name}}</option> {{/each}}';

  var $country = $('select[data-role="country_select"]'),
    $city = $('select[data-role="city_select"]');

  var cityInfo;

  function getCity(origin) {
    cityInfo.forEach(function(ele) {
      if (ele.country_id == origin) {
        var cities = ele.cities.sort(function(a, b) {
          return a2z(a.city_name, b.city_name);
        });
        var template = Handlebars.compile(city_tpl);
        $city.html(template(cities));
        return false;
      }
    });
  }

  async function getCountry() {
    // klook.ajaxGet(klook.parseAjaxUrl("/opersrv/cities"), function(resp) {
    //     if (resp.success) {
    //         cityInfo = resp.result.items.sort(function(a, b) {
    //             return a2z(a.country_name, b.country_name)
    //         });
    //         var template = Handlebars.compile(country_tpl);
    //         $country.html(template(cityInfo));
    //     } else {
    //         klook.popupAlert(resp.error.message);
    //     }
    // });
    let resp = await ADMIN_API.getOpersrvCities({
      just_throw: false
    });

    if (resp.success) {
      cityInfo = resp.result.items.sort(function(a, b) {
        return a2z(a.country_name, b.country_name);
      });
      var template = Handlebars.compile(country_tpl);
      $country.html(template(cityInfo));
    } else {
      klook.popupAlert(resp.error.message);
    }
  }

  $country.on("change", function() {
    getCity($(this).val());
  });

  getCountry();
}

function input2point(obj) {
  var $amountInput = obj;
  $amountInput.val(
    $amountInput
      .val()
      .replace(/[^\d.]/g, "")
      //只能输入小数点后两位
      .replace(".", "$#$")
      .replace(/\./g, "")
      .replace("$#$", ".")
      .replace(/^(-)*(\d+)\.(\d\d).*$/, "$1$2.$3")
  );
}

function parseDotPrice(price, currency) {
  if (dotCurrency.indexOf(currency) < 0) {
    price = parseFloat(price).toFixed(2);
  }
  return price;
}

function checkActLock(activity_id, params = null) {
  editlock.init(activity_id, params);
}

function changePriceError(resp) {
  return new Promise((resolve, reject) => {
    let $vm = new Vue();
    let h = $vm.$createElement;
    let message = "";

    if (resp.success) {
      message = (resp.result && resp.result.message) || "";
      if (message && message.indexOf("warn conditions cost > selling") !== -1) {
        message = __("change_price_warn_cheap");
      } else if (
        message &&
        message.indexOf("warn conditions cost < selling * 0.8") !== -1
      ) {
        message = __("change_price_warn_dear");
      } else if (message) {
        // klook.popupAlert(message)
      } else {
        message = __("global_success");
      }
    } else {
      switch (resp.error.code) {
        case "030002":
        case "030007": {
          message = __("change_price_system_err");
          break;
        }
        // case "030006": {
        //   message = __("change_price_incorrect");
        //   break;
        // }
        // case "030008": {
        //   msg = __("change_price_err");
        //   break;
        // }
        // case "030009": {
        //   msg = __("change_price_re-enter");
        //   break;
        // }
        default: {
          message = resp.error.message || JSON.stringify(resp.error);
        }
      }
    }
    $vm
      .$msgbox({
        title: " ",
        message: h("div", {
          style: "max-height: calc(100vh - 200px); overflow: auto;",
          domProps: {
            innerHTML: message.replace(/\n/gi, "<br /><br />")
          }
        }),
        showCancelButton: false,
        confirmButtonText: $vm.$t("global_button_ok")
      })
      .then(action => {
        setTimeout(() => {
          resolve();
        }, 300);
      });
  });
}

function a2z(a, b) {
  // sort by alphabet
  var nameA = a.toUpperCase(); // ignore upper and lowercase
  var nameB = b.toUpperCase(); // ignore upper and lowercase
  if (nameA < nameB) {
    return -1;
  }
  if (nameA > nameB) {
    return 1;
  }
  // names must be equal
  return 0;
}

// FIXME: 不显示全局 loading 的 url 白名单
// （后续最好改成通过接口参数配置控制是否显示 loading 的方式）
const NoLoadingUrlWhiteList = [
  "editprotect",
  "/v1/orderadminserv/sd/draft/import_booking_list/progress",
  // order search
  "/admin_proxy/v1/orderbasesrv/ticket/summary/by_booking_refer_no",
  "/v1/orderadminserv/alter/ticket/tags",
  "/v1/orderadminserv/tasklist/get",
  "/v1/cegadminsrv/merchant/list",
  "/v1/orderadminserv/notes",
  "/v1/cegonesrv/booking/destination",
  "/v1/expadminsrv/booking/service/get_booking_extra",
  "/v1/railadminserv/ptp/order/bizdata",
  "/v1/railadminserv/flight/order/bizdata"
];
function addLoading(options = {}) {
  //
  if (options.hidden) {
    $(document).off("ajaxSend");
    $(document).off("ajaxComplete");
    return;
  }
  var loading;
  var send = 0;
  var stop = 0;
  var interval = 200; // milliseconds, used to continue the loading animation between the sequential requests' interval. i.e, when $.ajaxStop will trigger
  /*
   * XXX
   * this is problematic
   * es6/webpack? `import` will precede `module require`
   * so when ajax sent from import file,
   * the `ajaxSend` event won't be triggered.
   */
  $(document).ajaxSend((e, xhr, settings) => {
    let url = settings.url.toString();
    let isNoLoading = NoLoadingUrlWhiteList.some(cur => url.match(cur));
    if (!isNoLoading) {
      send++;
      loading || (loading = new Vue().$loading(options));
      loading.setText(`${stop + 1} / ${send}`);
    }
  });
  /*
   * FIXME
   * the `stop` counter will never equals `send`
   * if some ajax request was sent before calling this fn.
   *
   * this may be conbined with ajaxStop for a better solution!!
   * we should stop loading as long there's no ajax request for a litter interval
   */
  $(document).ajaxComplete((e, xhr, settings) => {
    let url = settings.url.toString();
    let isNoLoading = NoLoadingUrlWhiteList.some(cur => url.match(cur));
    if (!isNoLoading) {
      stop++;
      loading && loading.setText(`${stop} / ${send}`);
    }

    setTimeout(() => {
      if (!$.active) {
        stop = send = 0; // reset
        loading && loading.close && loading.close();
        loading = null;
      }
    }, interval);
  });
}

// 获取语言翻译状态
async function getTranslateStatus(activity_id) {
  const res = await ADMIN_API.getActTranslateStatus(activity_id);
  const statuses = _.get(res, "result.statuses.0.items") || [];
  const status_obj = statuses.reduce((acc, cur) => {
    acc[cur.language] = cur.status;
    return acc;
  }, {});
  return status_obj;
}

// 获取屏蔽的语言列表
async function get_blocklist(id) {
  const res = await ADMIN_API.getActBlocklist(id);
  const list = (_.get(res, "result.statuses.0.items") || []).filter(
    // =1 为被block的语言
    v => v.status === 1
  );
  return list.map(v => v.language);
}

// 语言管理弹窗
async function manageLang({
  id,
  is_edit,
  url,
  template_id,
  act_status,
  actData
} = {}) {
  /*
   * explaining passing `url` here:
   * initially, I passed a callback to `$vm.confirm` like manageLang(..., cb) => $vm.confirm(...) => cb(...)
   * the `$vm.confirm` function freezed after vue instantiated. and ignores furthur `cb`s passed in.
   * then caused confirm callback to open the same url every time.
   *
   * I DONT like the pattern of instantiate-once-then-refresh-status way tho.
   */
  const resp = (await ADMIN_API.get_act_lang_status(id)).result;
  const blocked_languages = await get_blocklist(id);
  let {
    status,
    en_standardization_status,
    source_language,
    is_support_published_with_ai
  } = resp; // 语言状态array, 以及 en_US 是否结构化完成
  // 获取语言翻译状态
  if (!is_edit) {
    try {
      const translate_status_obj = await getTranslateStatus(id);
      status.forEach(v => {
        v.translation_status = translate_status_obj[v.language];
      });
    } catch (e) {
      console.log(e);
    }
  }

  // if only one language
  if (is_edit && status.every(item => item.status === 0)) {
    if (actData.sub_category_id === 2 && actData.had_been_inherit_spu) {
      const path = `mspa/experiencesadmincommon/aid/tours/basic`;
      const src = klook.strformat(
        `/{0}${path}/{1}?edit_status={2}&is_am={3}`,
        KLK_LANG_PATH,
        actData.activity_id,
        actData.status,
        false
      );
      window.open(src, "_blank");
      return;
    }
    window.open(`/${KLK_LANG_PATH}act/activity/add/${id}`);
    return;
  }

  // has multi lang
  if (!window.VmanageLang) {
    // if $vm not instanciated
    window.VmanageLang = new Vue({
      el: "#ml",
      render: h => h(manage_lang), // another way to proxy `dialogVisible` is to pass props: https://stackoverflow.com/a/39847465
      data: {
        dialogVisible: true,
        tableData: [],
        activity_id: id,
        act_status,
        is_edit,
        is_am: window.KLK_PAGE_DATA._roles.is_am,
        url,
        template_id, // 活动类别
        is_wifi: [7, 8].includes(template_id),
        en_standardization_status, // 英文是否已经结构化
        blocked_languages,
        sourceLanguage: "",
        isSupportPublishedWithAi: false,
        actData: {}
      },
      methods: {
        async confirm(edit, ref) {
          const params = {
            user_type: this.is_am ? 0 : 1,
            language: this.is_am ? "ALL" : edit
          };
          if (this.actData.inherit_spu_id) {
            params.package_id = this.actData.inherit_spu_id;
          }
          await editlock.checkLock(this.activity_id, false, null, params);
          window.localStorage.setItem("act_edit_lang", edit);
          window.localStorage.setItem("act_ref_lang", ref);
          debugger;
          var url = klook.urlParam(
            "lang",
            edit,
            klook.urlParam("ref", ref, this.url)
          );
          console.log(url,'url');
          window.open(url, "_blank");
        }
      }
    });
  }

  // refresh status everytime !!!
  window.VmanageLang.dialogVisible = true;
  window.VmanageLang.tableData = status;
  window.VmanageLang.is_edit = is_edit;
  window.VmanageLang.activity_id = id;
  window.VmanageLang.act_status = act_status;
  window.VmanageLang.url = url;
  window.VmanageLang.is_wifi = [7, 8].includes(template_id);
  window.VmanageLang.template_id = template_id;
  window.VmanageLang.en_standardization_status = en_standardization_status;
  window.VmanageLang.blocked_languages = blocked_languages;
  window.VmanageLang.sourceLanguage = source_language;
  window.VmanageLang.isSupportPublishedWithAi = is_support_published_with_ai;
  window.VmanageLang.actData = actData;
}

function inputAdjustable() {
  /*
   * define jQuery plugin
   * https://github.com/zaus/MyJsUtilities/blob/master/jquery-gettextsize.js
   */
  !(function($) {
    // see discussion at https://coderwall.com/p/ziynxq;
    // inspired by https://coderwall.com/p/kdi8ua

    var N = function(key) {
        return "getTextSize." + key;
      },
      fontMapping = function($o, font) {
        //return {"font": font.font || $o.css('font')};
        var result = {}; // don't affect original object
        $.each(font, function(prop, val) {
          result[prop] = val || $o.css(prop);
        });
        return result;
      };

    $.fn.getTextSize = function(dimension, text, font) {
      /// <summary>
      ///		Get width or height of element(s)
      ///		<example><code>$('.el-one, .el-two, a').getTextSize(/*'height'*/ /*, false or new string*/ /*, font construct*/).join('px, ')+'px'</code></example>
      /// </summary>

      dimension = dimension || "width";
      // figure out what font aspects we're concerned with
      if (typeof font === "string") {
        font = {
          font: font
        };
      }
      // include other common style properties that affect sizing
      font = $.extend(
        {
          font: false,
          "text-transform": false,
          "letter-spacing": false
        },
        font
      );

      // allow multiple calculations
      return $.map($(this), function(o) {
        var $o = $(o),
          $fake = $o.data(N("fake"));
        if (!$fake) {
          // cloning causes duplication issues on subsequent calls
          // can attach to parent, but may be unnecessary parsing vs. body if updating font each time
          $fake = $("<span>")
            .hide()
            .addClass("placeholder")
            .empty()
            .appendTo(document.body);
          $o.data(N("fake"), $fake);
        }
        return (
          $fake
            .html(text || $o.val() || $o.text())
            .css(fontMapping($o, font))
            // eslint-disable-next-line no-unexpected-multiline
            [dimension]()
        );
      });
    };
  })(jQuery);

  // this should be added into seperate js file
  // $(document).on('input change focus', 'input', function() {
  //     var $input = $(this)
  //     $input.css('width', $input.getTextSize()[0] + 30);
  // })
}

const act_templates = [
  {
    // NOTE `act_templates` in discount page is seperately maintianed, rememeber to sync that part whenever change
    id: 1,
    name: __("act_attractions_shows"),
    desc: __("act_attractions_shows_desc")
  },
  {
    id: 2,
    name: __("act_tour_sightseeing"),
    desc: __("act_tour_sightseeing_desc")
  },
  {
    id: 3,
    name: __("act_activities_experiences"),
    desc: __("act_activities_experiences_desc")
  },
  {
    id: 4,
    name: __("act_food_wellness"),
    desc: __("act_food_wellness_desc")
  },
  {
    id: 5,
    name: __("act_transport_wifi"),
    desc: __("act_transport_wifi_desc")
  },
  // ...klook.mergeIf(window.KLK_PAGE_DATA._roles['ACT-SPU-CREATE'], [ // access control
  {
    id: 7,
    name: __("template_wifi"),
    desc: __("template_wifi_desc")
  },
  {
    id: 8,
    name: __("template_sim"),
    desc: __("template_sim_desc")
  },
  {
    id: 9,
    name: "YSIM",
    desc: __("template_ysim_desc")
  },
  // ]),
  {
    id: 10,
    name: __("acc_jr_pass_voucher"),
    desc: __("acc_jr_pass_voucher_desc")
  },
  // {

  //     id: 12,
  //     name: 'Rail China',
  // }
  {
    id: 14,
    name: __("act_hotel_voucher"),
    desc: __("act_hotel_voucher_desc")
  },
  {
    id: 19,
    name: __("e_gift_card"),
    desc: ""
  }
];

const getActTemplateList = () => {
  return act_templates;
  // return (await ADMIN_API.getTemplateList()).result.map(item => {
  //   let name = KLK_LANG === "zh-CN"
  //                       ? item.name_ch
  //                       : KLK_LANG === "zh-TW"
  //                         ? item.name_tw
  //                         : item.name_en || "",
  //       desc = act_templates.reduce((acc, curr) => {
  //         if (curr.id == item.id) {
  //           return curr.desc
  //         }
  //         return acc
  //       }, '')
  //
  //   if (name == '') {
  //     name = item.name_en
  //   }
  //
  //   return Object.assign(item, {
  //     name,
  //     desc
  //   })
  // });
};

const act_status_map = {
  2: __("global_draft"),
  3: __("act_list_content_to_edit"),
  4: __("act_list_in_preview"),
  1: __("global_published"),
  0: __("global_unpublished")
};
const act_labels = [
  "New Activity",
  "Editor Choice",
  // "Bestseller",
  "Normal"
  // "Percetage Off",
  // "Skip The Line"
];

function syncParamMixin(keyParamMap, modifyHistory = false) {
  //  * NOTE:
  //  * k: key, p: param

  // mixins: [{
  //     syncParamMixin([{
  //         k: 'batch_id',
  //         p: 'batch',
  //         setFn: v => +v,
  //     }, {
  //         k: 'draft_id',
  //         p: 'draft',
  //         setFn: v => +v,
  //     }, {
  //         k: 'step',
  //         p: 'step',
  //     }]),
  // }]
  var setParam = (p, v) => {
    var params = new window.URLSearchParams(window.location.search);
    if (!_.isNil(v)) {
      params.set(p, encodeURIComponent(v));
    } else {
      params.delete(p);
    }
    var new_href = `${window.location.origin}${window.location.pathname}${
      [...params.entries()].length ? `?${params}` : ""
    }`;
    if (modifyHistory) {
      window.history.pushState({}, "", new_href);
    } else {
      window.history.replaceState({}, "", new_href);
    }
  };
  var getParam = p => {
    var params = new window.URLSearchParams(window.location.search);
    if (params.get(p) !== null) {
      return decodeURIComponent(params.get(p));
    } else {
      return null;
    }
  };

  // init an empty mixin obj
  var $mixin = {
    watch: {}
  };

  // vue data watch key can be composed like `form.somekey`
  keyParamMap.forEach(({ k, p, getFn = _.identity }) => {
    $mixin.watch[k] = v => {
      var newVal = getFn(v);
      setParam(p, newVal);
    };
  });

  /*
   * set data when mounted.
   * Edge case:
   * 'page', 'page_length' won't be reactive and will be overlay by `search`
   * especially when table search will reset `page` to 1
   */
  $mixin.mounted = function() {
    // vue method, don't use  `=>` here
    keyParamMap.forEach(({ k, p, setFn = _.identity }) => {
      if (getParam(p)) {
        _.set(this, k, setFn(getParam(p)));
      }
    });
  };

  return $mixin;
}

/**
 * SS-621 执行下架 activity操作时，需要检查相应的状态, 在多个页面用到，因此放到这里
 * @param {*} id
 * @param {*} type
 */
function checkActivityInfo(id, type) {
  if (!window.Promise) {
    klook.popupAlert(
      "当前浏览器不支持某些功能，建议使用chrome最新版进行重试操作～"
    );
    return;
  }
  return new Promise(async function(resolve, reject) {
    // klook.ajaxGet(klook.parseAjaxUrl(('/prosrv/prices/item/checkinfo')), {
    //     item_id: id,
    //     item_type: type
    // }, (res) => {
    //     if (res.success && res.result) {
    //         var redeemBatchSummary = (res.result.redeem_batch_summary || []).map(item => item.brand_type);
    //         var thridApiSummary = (res.result.third_party_api || {}).is_api;
    //         var _callback = () => {
    //             resolve();
    //         };
    //         if (redeemBatchSummary.length > 0 && thridApiSummary === 1) {
    //             klook.popupDialog(__('act_activity_unpublish_' + type + '_error3', id, redeemBatchSummary.join(',')), _callback);
    //         } else if (redeemBatchSummary.length > 0) {
    //             klook.popupDialog(__('act_activity_unpublish_' + type + '_error1', id, redeemBatchSummary.join(',')), _callback);
    //         } else if (thridApiSummary === 1) {
    //             klook.popupDialog(__('act_activity_unpublish_' + type + '_error2', id), _callback);
    //         }
    //         if (redeemBatchSummary.length <= 0 && thridApiSummary !== 1) {
    //             resolve();
    //         }
    //     } else {
    //         klook.popupAlert('Error in /prosrv/prices/item/checkinfo: ' + (res.error && res.error.message));
    //         resolve();
    //     }
    // });

    var res = await ADMIN_API.pricesItemCheckInfo({
      params: {
        item_id: id,
        item_type: type
      },
      just_throw: false
    });

    if (res.success && res.result) {
      var redeemBatchSummary = (res.result.redeem_batch_summary || []).map(
        item => item.brand_type
      );
      var thridApiSummary = (res.result.third_party_api || {}).is_api;
      var _callback = () => {
        resolve();
      };
      if (redeemBatchSummary.length > 0 && thridApiSummary === 1) {
        klook.popupDialog(
          __(
            "act_activity_unpublish_" + type + "_error3",
            id,
            redeemBatchSummary.join(",")
          ),
          _callback
        );
      } else if (redeemBatchSummary.length > 0) {
        klook.popupDialog(
          __(
            "act_activity_unpublish_" + type + "_error1",
            id,
            redeemBatchSummary.join(",")
          ),
          _callback
        );
      } else if (thridApiSummary === 1) {
        klook.popupDialog(
          __("act_activity_unpublish_" + type + "_error2", id),
          _callback
        );
      }
      if (redeemBatchSummary.length <= 0 && thridApiSummary !== 1) {
        resolve();
      }
    } else {
      klook.popupAlert(
        "Error in /prosrv/prices/item/checkinfo: " +
          (res.error && res.error.message)
      );
      resolve();
    }
  });
}
var setPathname = (pathname, cur_href = window.location.href) =>
  new URI(cur_href).pathname(klook.getLangUrl(pathname)).toString();
var setQuery = (query_obj, cur_href = window.location.href) =>
  new URI(cur_href).setQuery(query_obj).toString();

async function getCopyEnglishObj() {
  return {};
}

const getCMSUrl = ({ type, id }) => {
  /*
    OBTG string = "OBTG" // OBT Group 多语言表taxon_admin_group_i18n/ collection_id=5
    OBTT string = "OBTT" // OBT Title 多语言表taxon_admin_title_i18n/ collection_id=5
    ATTR string = "ATTR" // Attribute Warehouse 多语言表taxon_attr_i18n/ collection_id=6
    ATTV string = "ATTV" // Attribute value 多语言表taxon_attr_value_i18n/ collection_id=6
    FRON string = "FRON" // Fronted Config 多语言表taxon_frontend_config_section_i18n/ collection_id=5
  */
  if (["Attr.", "RichT.", "Sing.", "Img.", "Dura."].includes(type)) {
    type = "widget";
  }

  if (["Gr."].includes(type)) {
    type = "group";
  }

  const dict = {
    group: {
      collection_id: 5,
      text_keys: "OBTG"
    },
    widget: {
      collection_id: 5,
      text_keys: "OBTT"
    },
    attr: {
      collection_id: 6,
      text_keys: "ATTR"
    },
    attr_value: {
      collection_id: 6,
      text_keys: "ATTV"
    },
    front: {
      collection_id: 5,
      text_keys: "FRON"
    }
  };

  if (!type || !Object.prototype.hasOwnProperty.call(dict, type)) {
    throw new Error("Lack of params");
  }

  const { collection_id, text_keys } = dict[type];

  return `/mspa/platform/loc_cms/text_list?collection_id=${collection_id}&text_keys=${text_keys}-${id}`;
};

const getCMSUrlHtmlStr = ({ type, id } = {}) => {
  const href = getCMSUrl({ type, id });

  return `<el-button type="text"><a target="_blank" href="${href}" style="color: #20a0ff; margin-top: 10px;display: block;">View text IDs on L10 CMS</a></el-button>`;
};

module.exports = {
  // constants
  deps: [
    "UED",
    "Tech",
    "Content",
    "Marketing",
    "BD&Sales",
    "Operation",
    "Support",
    "Finanace"
  ],
  ui: {
    toggleSlider
  },
  act_templates,
  getActTemplateList,
  act_status_map,
  act_labels,

  // methods
  a2z,
  getLangTextBylang,
  getAllLangOption,
  checkActLock,
  initFilterLan,
  translators,
  input2point,
  isActive,
  hasParameter,
  checkHide,
  parseLanguage: _.noop,
  setSessionStorage,
  initLang,
  initCountryAndCity,
  initCountryAndCitySelect,
  initBannerCountry,
  initCities,
  chkAllSize,
  chkAll,
  dotCurrency,
  parseDotPrice,
  isRepeat,
  changePriceError,
  mandatoryLang,
  mandatoryLangtype,
  btnLoading,
  // checkActModifiable,
  addLoading,
  getRefLang,
  getEditLang,
  getUserLangs,
  inputAdjustable,
  bestMatchLang,
  manageLang,
  syncParamMixin,
  checkActivityInfo,
  setPathname,
  setQuery,
  getCopyEnglishObj,

  // @dep
  // editStatusAction,
  // checkFnbMenu,
  getCMSUrl,
  getCMSUrlHtmlStr
};
